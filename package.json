{"name": "onebox-email-system", "version": "1.0.0", "description": "Centralized email aggregator with AI analysis and smart features", "main": "dist/server.js", "scripts": {"dev": "nodemon", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:system": "node test-system.js", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "setup": "chmod +x setup.sh && ./setup.sh", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "keywords": ["email", "aggregator", "ai", "imap", "elasticsearch"], "author": "Your Name", "license": "MIT", "dependencies": {"@elastic/elasticsearch": "^8.11.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "imap": "^0.8.19", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mailparser": "^3.6.5", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "openai": "^4.20.1", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cheerio": "^0.22.35", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/imap": "^0.8.40", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/mailparser": "^3.4.6", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}}