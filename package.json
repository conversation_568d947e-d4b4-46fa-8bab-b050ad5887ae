{"name": "onebox-email-system", "version": "1.0.0", "description": "Centralized email aggregator with AI analysis and smart features", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:system": "node test-system.js", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "setup": "chmod +x setup.sh && ./setup.sh", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "keywords": ["email", "aggregator", "ai", "imap", "elasticsearch"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "imap": "^0.8.19", "mailparser": "^3.6.5", "@elastic/elasticsearch": "^8.11.0", "openai": "^4.20.1", "axios": "^1.6.2", "node-cron": "^3.0.3", "winston": "^3.11.0", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1", "cheerio": "^1.0.0-rc.12", "ioredis": "^5.3.2", "socket.io": "^4.7.4", "multer": "^1.4.5-lts.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/imap": "^0.8.40", "@types/uuid": "^9.0.7", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/cheerio": "^0.22.35", "@types/jest": "^29.5.8", "typescript": "^5.3.2", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "prettier": "^3.1.0", "jest": "^29.7.0", "ts-jest": "^29.1.1"}, "engines": {"node": ">=18.0.0"}}