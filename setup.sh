#!/bin/bash

# Onebox Email System Setup Script
echo "🚀 Setting up Onebox Email System..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install backend dependencies
echo "📦 Installing backend dependencies..."
npm install

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd frontend
npm install
cd ..

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration before starting the server"
else
    echo "✅ .env file already exists"
fi

# Create logs directory
mkdir -p logs

# Build TypeScript
echo "🔨 Building TypeScript..."
npm run build

echo ""
echo "✅ Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Edit .env file with your configuration:"
echo "   - Add your OpenAI API key"
echo "   - Configure email accounts (Gmail/Outlook with app passwords)"
echo "   - Set up Elasticsearch and Redis URLs"
echo "   - Configure Slack webhook (optional)"
echo ""
echo "2. Start required services:"
echo "   - Elasticsearch: Download and run locally or use cloud service"
echo "   - Redis: Install and run locally or use cloud service"
echo ""
echo "3. Start the application:"
echo "   Backend: npm run dev"
echo "   Frontend: cd frontend && npm run dev"
echo ""
echo "4. Access the application:"
echo "   Frontend: http://localhost:5173"
echo "   Backend API: http://localhost:3000"
echo "   Health check: http://localhost:3000/health"
echo ""
echo "📚 For detailed setup instructions, see README.md"
