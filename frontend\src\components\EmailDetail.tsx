import React, { useState } from 'react';
import { EmailDetailProps, SuggestedReply } from '../types';
import { emailService } from '../services/emailService';

const EmailDetail: React.FC<EmailDetailProps> = ({ email, onUpdate }) => {
  const [loading, setLoading] = useState(false);
  const [suggestedReply, setSuggestedReply] = useState<SuggestedReply | null>(null);
  const [showReplyBox, setShowReplyBox] = useState(false);

  const handleToggleStar = async () => {
    try {
      await emailService.toggleStar(email.id, !email.isStarred);
      onUpdate(email.id, { isStarred: !email.isStarred });
    } catch (error) {
      console.error('Error toggling star:', error);
    }
  };

  const handleCategorize = async () => {
    setLoading(true);
    try {
      const analysis = await emailService.categorizeEmail(email.id);
      onUpdate(email.id, { aiCategory: analysis.category });
    } catch (error) {
      console.error('Error categorizing email:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGetSuggestedReply = async () => {
    setLoading(true);
    try {
      const reply = await emailService.getSuggestedReply(email.id);
      setSuggestedReply(reply);
      setShowReplyBox(true);
    } catch (error) {
      console.error('Error getting suggested reply:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="card">
      {/* Header */}
      <div className="card-header">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              {email.subject}
            </h2>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>From: {email.from.name || email.from.address}</span>
              <span>•</span>
              <span>{emailService.formatDate(email.date)}</span>
              {email.aiCategory && (
                <>
                  <span>•</span>
                  <span className={`badge ${emailService.getCategoryColorClass(email.aiCategory)}`}>
                    {emailService.getCategoryDisplayName(email.aiCategory)}
                  </span>
                </>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleToggleStar}
              className={`p-2 rounded-md ${
                email.isStarred
                  ? 'text-yellow-500 hover:text-yellow-600'
                  : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              ⭐
            </button>
          </div>
        </div>
      </div>

      {/* Body */}
      <div className="card-body">
        {/* Recipients */}
        <div className="mb-4 text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <span className="font-medium">To:</span>
            <span>{email.to.map(addr => addr.name || addr.address).join(', ')}</span>
          </div>
          {email.cc && email.cc.length > 0 && (
            <div className="flex items-center space-x-2 mt-1">
              <span className="font-medium">CC:</span>
              <span>{email.cc.map(addr => addr.name || addr.address).join(', ')}</span>
            </div>
          )}
        </div>

        {/* Email Content */}
        <div className="prose max-w-none">
          {email.body.html ? (
            <div dangerouslySetInnerHTML={{ __html: email.body.html }} />
          ) : (
            <div className="whitespace-pre-wrap">{email.body.text}</div>
          )}
        </div>

        {/* Attachments */}
        {email.attachments && email.attachments.length > 0 && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-900 mb-3">
              Attachments ({email.attachments.length})
            </h4>
            <div className="space-y-2">
              {email.attachments.map((attachment, index) => (
                <div key={index} className="flex items-center space-x-3 p-2 bg-gray-50 rounded-md">
                  <span className="text-gray-400">📎</span>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{attachment.filename}</p>
                    <p className="text-xs text-gray-500">
                      {attachment.contentType} • {Math.round(attachment.size / 1024)} KB
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="flex items-center space-x-3">
            <button
              onClick={handleGetSuggestedReply}
              disabled={loading}
              className="btn-primary"
            >
              {loading ? 'Loading...' : '💬 Suggest Reply'}
            </button>
            <button
              onClick={handleCategorize}
              disabled={loading}
              className="btn-secondary"
            >
              {loading ? 'Loading...' : '🤖 Re-categorize'}
            </button>
          </div>
        </div>

        {/* Suggested Reply */}
        {showReplyBox && suggestedReply && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-900 mb-3">
              Suggested Reply (Confidence: {Math.round(suggestedReply.confidence * 100)}%)
            </h4>
            <div className="bg-gray-50 rounded-md p-4">
              <textarea
                className="w-full h-32 p-3 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                defaultValue={suggestedReply.content}
              />
              <div className="mt-3 flex items-center justify-between">
                <p className="text-xs text-gray-500">
                  Reasoning: {suggestedReply.reasoning}
                </p>
                <div className="flex space-x-2">
                  <button className="btn-secondary text-xs">Edit</button>
                  <button className="btn-primary text-xs">Send</button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailDetail;
