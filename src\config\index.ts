import dotenv from 'dotenv';
import { AppConfig, EmailAccount } from '@/types';

dotenv.config();

export const config: AppConfig = {
  port: parseInt(process.env.PORT || '3000', 10),
  nodeEnv: process.env.NODE_ENV || 'development',

  // Development mode flags
  useMockServices: process.env.USE_MOCK_SERVICES === 'true' ||
                   (!process.env.ELASTICSEARCH_URL && !process.env.REDIS_URL),

  elasticsearch: {
    url: process.env.ELASTICSEARCH_URL || 'http://localhost:9200',
    username: process.env.ELASTICSEARCH_USERNAME || undefined,
    password: process.env.ELASTICSEARCH_PASSWORD || undefined,
  },
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
  },
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '',
    model: process.env.OPENAI_MODEL || 'gpt-4-turbo-preview',
  },
  slack: {
    webhookUrl: process.env.SLACK_WEBHOOK_URL || '',
    channel: process.env.SLACK_CHANNEL || '#email-notifications',
  },
  webhook: {
    externalUrl: process.env.EXTERNAL_WEBHOOK_URL || '',
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'default-secret-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
};

// Email accounts configuration
export const getEmailAccounts = (): EmailAccount[] => {
  const accounts: EmailAccount[] = [];
  
  // Parse email accounts from environment variables
  let accountIndex = 1;
  while (process.env[`EMAIL_ACCOUNT_${accountIndex}_EMAIL`]) {
    const email = process.env[`EMAIL_ACCOUNT_${accountIndex}_EMAIL`];
    const password = process.env[`EMAIL_ACCOUNT_${accountIndex}_PASSWORD`];
    const provider = process.env[`EMAIL_ACCOUNT_${accountIndex}_PROVIDER`] as 'gmail' | 'outlook';
    
    if (email && password && provider) {
      const imapConfig = getIMAPConfig(provider, email, password);
      
      accounts.push({
        id: `account_${accountIndex}`,
        email,
        password,
        provider,
        imapConfig,
        isActive: true,
      });
    }
    
    accountIndex++;
  }
  
  return accounts;
};

const getIMAPConfig = (provider: 'gmail' | 'outlook', email: string, password: string) => {
  const configs = {
    gmail: {
      host: process.env.GMAIL_IMAP_HOST || 'imap.gmail.com',
      port: parseInt(process.env.GMAIL_IMAP_PORT || '993', 10),
      secure: process.env.GMAIL_IMAP_SECURE === 'true',
    },
    outlook: {
      host: process.env.OUTLOOK_IMAP_HOST || 'outlook.office365.com',
      port: parseInt(process.env.OUTLOOK_IMAP_PORT || '993', 10),
      secure: process.env.OUTLOOK_IMAP_SECURE === 'true',
    },
  };
  
  return {
    ...configs[provider],
    auth: {
      user: email,
      pass: password,
    },
  };
};

// Validation
export const validateConfig = (): void => {
  const requiredEnvVars = [
    'OPENAI_API_KEY',
    'EMAIL_ACCOUNT_1_EMAIL',
    'EMAIL_ACCOUNT_1_PASSWORD',
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
  
  const emailAccounts = getEmailAccounts();
  if (emailAccounts.length === 0) {
    throw new Error('No email accounts configured. Please set up at least one email account.');
  }
  
  console.log(`✅ Configuration validated. Found ${emailAccounts.length} email account(s).`);
};
