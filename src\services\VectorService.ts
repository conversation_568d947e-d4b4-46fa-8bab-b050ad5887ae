import OpenAI from 'openai';
import { KnowledgeBase, SuggestedReply, Email } from '@/types';
import { config } from '@/config';
import logger from '@/utils/logger';

// Simple in-memory vector store for demonstration
// In production, you would use Pinecone, Weaviate, or similar
interface VectorDocument {
  id: string;
  content: string;
  embedding: number[];
  metadata: any;
}

export class VectorService {
  private openai: OpenAI;
  private documents: VectorDocument[] = [];
  private initialized = false;

  constructor() {
    this.openai = new OpenAI({
      apiKey: config.openai.apiKey,
    });
  }

  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Load default knowledge base
      await this.loadDefaultKnowledgeBase();
      this.initialized = true;
      logger.info('✅ Vector service initialized');
    } catch (error) {
      logger.error('❌ Failed to initialize vector service:', error);
      throw error;
    }
  }

  private async loadDefaultKnowledgeBase(): Promise<void> {
    const defaultKnowledge = [
      {
        id: 'company-info',
        content: `Our company is a leading provider of email management solutions. We specialize in AI-powered email categorization, automated responses, and intelligent email routing. Our main products include Onebox Email System, Smart Reply AI, and Email Analytics Dashboard.`,
        metadata: { type: 'company_info', tags: ['company', 'products'] },
      },
      {
        id: 'meeting-booking',
        content: `For scheduling meetings, please use our calendar booking link: https://cal.com/onebox-demo. We offer 30-minute discovery calls and 60-minute product demonstrations. Our team is available Monday through Friday, 9 AM to 5 PM EST.`,
        metadata: { type: 'template', tags: ['meeting', 'scheduling'] },
      },
      {
        id: 'pricing-info',
        content: `Our pricing starts at $29/month for the basic plan (up to 5 email accounts), $99/month for professional (up to 25 accounts), and $299/month for enterprise (unlimited accounts). All plans include AI categorization, Slack integration, and 24/7 support.`,
        metadata: { type: 'product_info', tags: ['pricing', 'plans'] },
      },
      {
        id: 'demo-request',
        content: `Thank you for your interest in our product! I'd be happy to schedule a personalized demo to show you how our AI-powered email system can streamline your workflow. The demo typically takes 30 minutes and covers email categorization, automated responses, and integration options.`,
        metadata: { type: 'template', tags: ['demo', 'sales'] },
      },
      {
        id: 'technical-support',
        content: `For technical support, please provide: 1) Description of the issue, 2) Steps to reproduce, 3) Error messages if any, 4) Your account email. Our technical team typically responds within 2-4 hours during business hours.`,
        metadata: { type: 'template', tags: ['support', 'technical'] },
      },
      {
        id: 'integration-info',
        content: `We support integrations with Gmail, Outlook, Slack, Zapier, HubSpot, Salesforce, and custom webhooks. Our API allows for custom integrations and we provide comprehensive documentation and SDKs for popular programming languages.`,
        metadata: { type: 'product_info', tags: ['integrations', 'api'] },
      },
    ];

    for (const knowledge of defaultKnowledge) {
      await this.addDocument(knowledge.id, knowledge.content, knowledge.metadata);
    }
  }

  public async addDocument(id: string, content: string, metadata: any = {}): Promise<void> {
    try {
      const embedding = await this.generateEmbedding(content);
      
      const document: VectorDocument = {
        id,
        content,
        embedding,
        metadata,
      };

      // Remove existing document with same ID
      this.documents = this.documents.filter(doc => doc.id !== id);
      
      // Add new document
      this.documents.push(document);

      logger.debug(`Added document to vector store: ${id}`);
    } catch (error) {
      logger.error(`Error adding document ${id} to vector store:`, error);
      throw error;
    }
  }

  public async removeDocument(id: string): Promise<void> {
    this.documents = this.documents.filter(doc => doc.id !== id);
    logger.debug(`Removed document from vector store: ${id}`);
  }

  public async searchSimilar(query: string, limit: number = 5): Promise<VectorDocument[]> {
    try {
      if (this.documents.length === 0) {
        return [];
      }

      const queryEmbedding = await this.generateEmbedding(query);
      
      // Calculate cosine similarity for each document
      const similarities = this.documents.map(doc => ({
        document: doc,
        similarity: this.cosineSimilarity(queryEmbedding, doc.embedding),
      }));

      // Sort by similarity (descending) and return top results
      return similarities
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit)
        .map(item => item.document);
    } catch (error) {
      logger.error('Error searching similar documents:', error);
      return [];
    }
  }

  public async generateContextualReply(email: Email, context?: string[]): Promise<SuggestedReply> {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      // Search for relevant knowledge
      const searchQuery = `${email.subject} ${email.body.text || ''}`;
      const relevantDocs = await this.searchSimilar(searchQuery, 3);

      // Combine provided context with retrieved knowledge
      const knowledgeContext = relevantDocs.map(doc => doc.content);
      const allContext = [...(context || []), ...knowledgeContext];

      // Generate reply using AI with context
      const prompt = this.buildReplyPrompt(email, allContext);
      
      const response = await this.openai.chat.completions.create({
        model: config.openai.model,
        messages: [
          {
            role: 'system',
            content: `You are an AI assistant that generates professional email replies using provided context and knowledge base. 
            Generate appropriate, contextual replies that are helpful and professional.
            
            Guidelines:
            - Use information from the knowledge base when relevant
            - Keep replies concise and professional
            - Include specific details like pricing, meeting links, or technical info when appropriate
            - Match the tone of the original email
            - Always end with a clear call-to-action or next step
            
            Respond with a JSON object containing:
            - content: the suggested reply text
            - confidence: number between 0-1 indicating confidence in the suggestion
            - sources: array of knowledge base sources used (use document IDs)
            - reasoning: brief explanation for the reply approach`,
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.3,
        max_tokens: 800,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      const suggestion = JSON.parse(content) as SuggestedReply;
      
      // Map document IDs to actual sources
      suggestion.sources = relevantDocs
        .filter(doc => suggestion.sources.includes(doc.id))
        .map(doc => doc.metadata.type || doc.id);

      logger.info(`Generated contextual reply with confidence: ${suggestion.confidence}`);
      return suggestion;

    } catch (error) {
      logger.error(`Error generating contextual reply for email ${email.id}:`, error);
      
      // Return fallback reply
      return {
        content: 'Thank you for your email. I will review it and get back to you soon.',
        confidence: 0.1,
        sources: [],
        reasoning: 'AI reply generation failed, using fallback response',
      };
    }
  }

  private async generateEmbedding(text: string): Promise<number[]> {
    try {
      const response = await this.openai.embeddings.create({
        model: 'text-embedding-ada-002',
        input: text,
      });

      return response.data[0].embedding;
    } catch (error) {
      logger.error('Error generating embedding:', error);
      throw error;
    }
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      throw new Error('Vectors must have the same length');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);

    if (normA === 0 || normB === 0) {
      return 0;
    }

    return dotProduct / (normA * normB);
  }

  private buildReplyPrompt(email: Email, context: string[]): string {
    let prompt = `
Email to reply to:

Subject: ${email.subject}
From: ${email.from.name || ''} <${email.from.address}>
Date: ${email.date.toISOString()}

Body:
${email.body.text || email.body.html || 'No content'}
    `;

    if (context && context.length > 0) {
      prompt += `\n\nKnowledge Base Context:\n${context.join('\n\n')}`;
    }

    prompt += '\n\nPlease generate an appropriate reply using the knowledge base context and provide your response in JSON format.';

    return prompt.trim();
  }

  public async addKnowledgeBase(knowledgeBase: KnowledgeBase): Promise<void> {
    await this.addDocument(knowledgeBase.id, knowledgeBase.content, knowledgeBase.metadata);
  }

  public async updateKnowledgeBase(knowledgeBase: KnowledgeBase): Promise<void> {
    await this.addDocument(knowledgeBase.id, knowledgeBase.content, knowledgeBase.metadata);
  }

  public async deleteKnowledgeBase(id: string): Promise<void> {
    await this.removeDocument(id);
  }

  public getDocumentCount(): number {
    return this.documents.length;
  }

  public getDocuments(): VectorDocument[] {
    return [...this.documents];
  }
}
