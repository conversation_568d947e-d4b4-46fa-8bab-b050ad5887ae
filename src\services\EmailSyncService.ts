import Imap from 'imap';
import { simple<PERSON><PERSON><PERSON>, ParsedMail } from 'mailparser';
import { v4 as uuidv4 } from 'uuid';
import { Server as SocketIOServer } from 'socket.io';

import { EmailAccount, Email, EmailAddress, Attachment } from '@/types';
import { getEmailAccounts } from '@/config';
import logger from '@/utils/logger';
import { ElasticsearchService } from './ElasticsearchService';
import { AIService } from './AIService';
import { NotificationService } from './NotificationService';

export class EmailSyncService {
  private emailAccounts: EmailAccount[];
  private imapConnections: Map<string, any> = new Map();
  private isRunning: boolean = false;
  private syncIntervals: Map<string, NodeJS.Timeout> = new Map();

  constructor(
    private elasticsearchService: ElasticsearchService,
    private aiService: AIService,
    private notificationService: NotificationService,
    private io: SocketIOServer
  ) {
    this.emailAccounts = getEmailAccounts();
  }

  public async startSynchronization(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Email synchronization is already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting email synchronization for all accounts...');

    for (const account of this.emailAccounts) {
      if (account.isActive) {
        await this.connectToAccount(account);
      }
    }
  }

  public async stopSynchronization(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    logger.info('Stopping email synchronization...');

    // Clear all intervals
    this.syncIntervals.forEach((interval) => {
      clearInterval(interval);
    });
    this.syncIntervals.clear();

    // Close all IMAP connections
    this.imapConnections.forEach((imap, accountId) => {
      try {
        imap.end();
        logger.info(`Closed IMAP connection for account: ${accountId}`);
      } catch (error) {
        logger.error(`Error closing IMAP connection for ${accountId}:`, error);
      }
    });
    this.imapConnections.clear();
  }

  private async connectToAccount(account: EmailAccount): Promise<void> {
    try {
      logger.info(`Connecting to ${account.provider} account: ${account.email}`);

      const imap = new Imap({
        user: account.imapConfig.auth.user,
        password: account.imapConfig.auth.pass,
        host: account.imapConfig.host,
        port: account.imapConfig.port,
        tls: account.imapConfig.secure,
        tlsOptions: { rejectUnauthorized: false }
      });
      this.imapConnections.set(account.id, imap);

      imap.once('ready', async () => {
        logger.info(`IMAP connection ready for ${account.email}`);
        await this.setupFolderSync(account, imap);
      });

      imap.once('error', (err: Error) => {
        logger.error(`IMAP error for ${account.email}:`, err);
        this.handleConnectionError(account, err);
      });

      imap.once('end', () => {
        logger.info(`IMAP connection ended for ${account.email}`);
        this.imapConnections.delete(account.id);
      });

      imap.connect();
    } catch (error) {
      logger.error(`Failed to connect to ${account.email}:`, error);
      throw error;
    }
  }

  private async setupFolderSync(account: EmailAccount, imap: any): Promise<void> {
    try {
      // Get list of folders
      imap.getBoxes((err: any, boxes: any) => {
        if (err) {
          logger.error(`Error getting boxes for ${account.email}:`, err);
          return;
        }

        // Focus on INBOX for now, can be extended to other folders
        this.syncFolder(account, imap, 'INBOX');
      });
    } catch (error) {
      logger.error(`Error setting up folder sync for ${account.email}:`, error);
    }
  }

  private async syncFolder(account: EmailAccount, imap: any, folderName: string): Promise<void> {
    imap.openBox(folderName, false, async (err: any, box: any) => {
      if (err) {
        logger.error(`Error opening ${folderName} for ${account.email}:`, err);
        return;
      }

      logger.info(`Opened ${folderName} for ${account.email}. Total messages: ${box.messages.total}`);

      // Fetch emails from the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Search for emails from the last 30 days
      imap.search(['SINCE', thirtyDaysAgo], (err: any, results: any) => {
        if (err) {
          logger.error(`Error searching emails for ${account.email}:`, err);
          return;
        }

        if (results.length > 0) {
          logger.info(`Found ${results.length} emails in the last 30 days for ${account.email}`);
          this.fetchEmails(account, imap, results, folderName);
        }
      });

      // Set up IDLE mode for real-time updates
      this.setupIdleMode(account, imap, folderName);
    });
  }

  private async fetchEmails(
    account: EmailAccount,
    imap: any,
    uids: number[],
    folderName: string
  ): Promise<void> {
    const fetch = imap.fetch(uids, {
      bodies: '',
      struct: true,
      envelope: true,
    });

    fetch.on('message', (msg: any, seqno: any) => {
      let buffer = '';
      let attributes: any;

      msg.on('body', (stream: any) => {
        stream.on('data', (chunk: any) => {
          buffer += chunk.toString('utf8');
        });
      });

      msg.once('attributes', (attrs: any) => {
        attributes = attrs;
      });

      msg.once('end', async () => {
        try {
          const parsed = await simpleParser(buffer);
          const email = await this.parseEmail(account, parsed, attributes, folderName);
          
          if (email) {
            await this.processNewEmail(email);
          }
        } catch (error) {
          logger.error(`Error parsing email for ${account.email}:`, error);
        }
      });
    });

    fetch.once('error', (err: any) => {
      logger.error(`Fetch error for ${account.email}:`, err);
    });

    fetch.once('end', () => {
      logger.info(`Finished fetching emails for ${account.email}`);
    });
  }

  private setupIdleMode(account: EmailAccount, imap: any, folderName: string): void {
    try {
      // Start IDLE mode
      imap.idle();
      logger.info(`Started IDLE mode for ${account.email} in ${folderName}`);

      // Listen for new emails
      imap.on('mail', (numNewMsgs: number) => {
        logger.info(`${numNewMsgs} new email(s) received for ${account.email}`);
        
        // Stop IDLE to fetch new emails
        imap.idle();
        
        // Search for the latest emails
        imap.search(['UNSEEN'], (err: any, results: any) => {
          if (err) {
            logger.error(`Error searching new emails for ${account.email}:`, err);
            return;
          }

          if (results.length > 0) {
            this.fetchEmails(account, imap, results, folderName);
          }

          // Restart IDLE mode
          setTimeout(() => {
            if (this.isRunning && this.imapConnections.has(account.id)) {
              imap.idle();
            }
          }, 1000);
        });
      });

      // Handle IDLE timeout (some servers require periodic IDLE restart)
      const idleInterval = setInterval(() => {
        if (this.isRunning && this.imapConnections.has(account.id)) {
          try {
            imap.idle();
            logger.debug(`Restarted IDLE for ${account.email}`);
          } catch (error) {
            logger.error(`Error restarting IDLE for ${account.email}:`, error);
          }
        } else {
          clearInterval(idleInterval);
        }
      }, 29 * 60 * 1000); // Restart IDLE every 29 minutes

      this.syncIntervals.set(`${account.id}_idle`, idleInterval);
    } catch (error) {
      logger.error(`Error setting up IDLE mode for ${account.email}:`, error);
    }
  }

  private async parseEmail(
    account: EmailAccount,
    parsed: ParsedMail,
    attributes: any,
    folderName: string
  ): Promise<Email | null> {
    try {
      const email: Email = {
        id: uuidv4(),
        messageId: parsed.messageId || uuidv4(),
        accountId: account.id,
        subject: parsed.subject || '(No Subject)',
        from: this.parseEmailAddress(parsed.from),
        to: this.parseEmailAddresses(parsed.to),
        cc: this.parseEmailAddresses(parsed.cc),
        bcc: this.parseEmailAddresses(parsed.bcc),
        date: parsed.date || new Date(),
        body: {
          text: parsed.text,
          html: typeof parsed.html === 'string' ? parsed.html : undefined,
        },
        attachments: this.parseAttachments(parsed.attachments),
        folder: folderName,
        flags: attributes.flags || [],
        uid: attributes.uid,
        isRead: attributes.flags?.includes('\\Seen') || false,
        isStarred: attributes.flags?.includes('\\Flagged') || false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      return email;
    } catch (error) {
      logger.error('Error parsing email:', error);
      return null;
    }
  }

  private parseEmailAddress(address: any): EmailAddress {
    if (!address) return { address: '' };
    
    if (Array.isArray(address)) {
      const first = address[0];
      return {
        name: first.name,
        address: first.address,
      };
    }
    
    return {
      name: address.name,
      address: address.address,
    };
  }

  private parseEmailAddresses(addresses: any): EmailAddress[] {
    if (!addresses) return [];
    
    if (Array.isArray(addresses)) {
      return addresses.map(addr => ({
        name: addr.name,
        address: addr.address,
      }));
    }
    
    return [{
      name: addresses.name,
      address: addresses.address,
    }];
  }

  private parseAttachments(attachments: any[]): Attachment[] {
    if (!attachments) return [];
    
    return attachments.map(att => ({
      filename: att.filename || 'unknown',
      contentType: att.contentType || 'application/octet-stream',
      size: att.size || 0,
      contentId: att.contentId,
    }));
  }

  private async processNewEmail(email: Email): Promise<void> {
    try {
      logger.info(`Processing new email: ${email.subject} from ${email.from.address}`);

      // Store email in Elasticsearch
      await this.elasticsearchService.indexEmail(email);

      // Analyze email with AI
      const aiAnalysis = await this.aiService.categorizeEmail(email);
      email.aiCategory = aiAnalysis.category;

      // Update email with AI analysis
      await this.elasticsearchService.updateEmail(email.id, { aiCategory: aiAnalysis.category });

      // Send notifications if needed
      if (aiAnalysis.category === 'interested') {
        await this.notificationService.sendSlackNotification(email, aiAnalysis.category);
        await this.notificationService.sendWebhook(email, aiAnalysis.category);
      }

      // Emit real-time update to frontend
      this.io.emit('new_email', {
        email,
        aiAnalysis,
      });

      logger.info(`Successfully processed email: ${email.id}`);
    } catch (error) {
      logger.error(`Error processing email ${email.id}:`, error);
    }
  }

  private handleConnectionError(account: EmailAccount, error: Error): void {
    logger.error(`Connection error for ${account.email}:`, error);
    
    // Remove the failed connection
    this.imapConnections.delete(account.id);
    
    // Attempt to reconnect after a delay
    setTimeout(() => {
      if (this.isRunning && account.isActive) {
        logger.info(`Attempting to reconnect to ${account.email}...`);
        this.connectToAccount(account).catch((err) => {
          logger.error(`Failed to reconnect to ${account.email}:`, err);
        });
      }
    }, 30000); // Wait 30 seconds before reconnecting
  }

  public getConnectionStatus(): { [accountId: string]: boolean } {
    const status: { [accountId: string]: boolean } = {};
    
    this.emailAccounts.forEach(account => {
      status[account.id] = this.imapConnections.has(account.id);
    });
    
    return status;
  }
}
