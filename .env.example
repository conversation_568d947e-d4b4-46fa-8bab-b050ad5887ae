# Server Configuration
PORT=3000
NODE_ENV=development

# Development Mode (set to true to use mock services without Elasticsearch/Redis)
USE_MOCK_SERVICES=true

# Database Configuration (only needed if USE_MOCK_SERVICES=false)
ELASTICSEARCH_URL=http://localhost:9200
ELASTIC<PERSON>ARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
REDIS_URL=redis://localhost:6379

# Email Configuration
# Gmail IMAP
GMAIL_IMAP_HOST=imap.gmail.com
GMAIL_IMAP_PORT=993
GMAIL_IMAP_SECURE=true

# Outlook IMAP
OUTLOOK_IMAP_HOST=outlook.office365.com
OUTLOOK_IMAP_PORT=993
OUTLOOK_IMAP_SECURE=true

# Email Accounts (Add multiple accounts as needed)
EMAIL_ACCOUNT_1_EMAIL=<EMAIL>
EMAIL_ACCOUNT_1_PASSWORD=your-app-password
EMAIL_ACCOUNT_1_PROVIDER=gmail

EMAIL_ACCOUNT_2_EMAIL=<EMAIL>
EMAIL_ACCOUNT_2_PASSWORD=your-app-password
EMAIL_ACCOUNT_2_PROVIDER=outlook

# AI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4-turbo-preview

# Slack Integration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_CHANNEL=#email-notifications

# Webhook Configuration
EXTERNAL_WEBHOOK_URL=https://webhook.site/your-unique-url

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# Vector Database (for RAG)
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
PINECONE_INDEX_NAME=onebox-knowledge-base

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
