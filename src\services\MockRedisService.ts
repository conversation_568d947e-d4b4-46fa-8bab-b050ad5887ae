import logger from '@/utils/logger';

/**
 * Mock Redis Service for development without Redis
 * Stores data in memory - not for production use
 */
export class MockRedisService {
  private store: Map<string, string> = new Map();
  private initialized = false;

  public async initialize(): Promise<void> {
    if (this.initialized) return;

    logger.info('🔄 Initializing Mock Redis Service (Development Mode)');
    this.initialized = true;
    logger.info('✅ Mock Redis Service initialized');
  }

  public async get(key: string): Promise<string | null> {
    return this.store.get(key) || null;
  }

  public async set(key: string, value: string, ttl?: number): Promise<void> {
    this.store.set(key, value);
    
    // Simulate TTL by removing after specified time
    if (ttl) {
      setTimeout(() => {
        this.store.delete(key);
      }, ttl * 1000);
    }
  }

  public async del(key: string): Promise<void> {
    this.store.delete(key);
  }

  public async exists(key: string): Promise<boolean> {
    return this.store.has(key);
  }

  public async keys(pattern: string): Promise<string[]> {
    const keys = Array.from(this.store.keys());
    
    // Simple pattern matching (only supports * wildcard)
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return keys.filter(key => regex.test(key));
    }
    
    return keys.filter(key => key === pattern);
  }

  public async flushall(): Promise<void> {
    this.store.clear();
  }

  public getSize(): number {
    return this.store.size;
  }
}
