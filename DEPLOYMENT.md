# Onebox Email System - Deployment Guide

This guide covers different deployment options for the Onebox Email System.

## Prerequisites

- Node.js 18+
- Docker and Docker Compose (for containerized deployment)
- Elasticsearch 8.x
- Redis 7.x
- OpenAI API key
- Email accounts with app passwords enabled

## Quick Start (Development)

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd onebox-email-system
   npm run setup
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start Services**
   ```bash
   # Start Elasticsearch and Redis (using Docker)
   docker run -d --name elasticsearch -p 9200:9200 -e "discovery.type=single-node" -e "xpack.security.enabled=false" docker.elastic.co/elasticsearch/elasticsearch:8.11.0
   docker run -d --name redis -p 6379:6379 redis:7-alpine

   # Start backend
   npm run dev

   # Start frontend (in another terminal)
   cd frontend
   npm run dev
   ```

4. **Test the System**
   ```bash
   npm run test:system
   ```

## Docker Deployment

### Using Docker Compose (Recommended)

1. **Prepare Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Start All Services**
   ```bash
   npm run docker:up
   ```

3. **Check Logs**
   ```bash
   npm run docker:logs
   ```

4. **Access the Application**
   - Frontend: http://localhost
   - Backend API: http://localhost:3000
   - Elasticsearch: http://localhost:9200

5. **Stop Services**
   ```bash
   npm run docker:down
   ```

### Manual Docker Deployment

1. **Build Images**
   ```bash
   # Backend
   docker build -f Dockerfile.backend -t onebox-backend .

   # Frontend
   docker build -f frontend/Dockerfile -t onebox-frontend ./frontend
   ```

2. **Run Services**
   ```bash
   # Network
   docker network create onebox-network

   # Elasticsearch
   docker run -d --name elasticsearch --network onebox-network -p 9200:9200 -e "discovery.type=single-node" -e "xpack.security.enabled=false" docker.elastic.co/elasticsearch/elasticsearch:8.11.0

   # Redis
   docker run -d --name redis --network onebox-network -p 6379:6379 redis:7-alpine

   # Backend
   docker run -d --name onebox-backend --network onebox-network -p 3000:3000 --env-file .env onebox-backend

   # Frontend
   docker run -d --name onebox-frontend --network onebox-network -p 80:80 onebox-frontend
   ```

## Production Deployment

### Cloud Deployment (AWS/GCP/Azure)

1. **Infrastructure Setup**
   - Elasticsearch: Use managed service (AWS OpenSearch, Elastic Cloud)
   - Redis: Use managed service (AWS ElastiCache, Google Memorystore)
   - Load Balancer: For high availability
   - SSL Certificate: For HTTPS

2. **Environment Configuration**
   ```bash
   # Production .env
   NODE_ENV=production
   PORT=3000
   ELASTICSEARCH_URL=https://your-elasticsearch-cluster.com
   REDIS_URL=redis://your-redis-cluster.com:6379
   OPENAI_API_KEY=your-production-api-key
   # ... other production settings
   ```

3. **Security Considerations**
   - Use environment variables for secrets
   - Enable HTTPS/SSL
   - Configure firewall rules
   - Set up monitoring and logging
   - Regular security updates

### Kubernetes Deployment

1. **Create Kubernetes Manifests**
   ```yaml
   # k8s/namespace.yaml
   apiVersion: v1
   kind: Namespace
   metadata:
     name: onebox
   ```

2. **Deploy to Kubernetes**
   ```bash
   kubectl apply -f k8s/
   ```

## Configuration

### Required Environment Variables

```bash
# Server
PORT=3000
NODE_ENV=production

# Database
ELASTICSEARCH_URL=http://localhost:9200
REDIS_URL=redis://localhost:6379

# Email Accounts
EMAIL_ACCOUNT_1_EMAIL=<EMAIL>
EMAIL_ACCOUNT_1_PASSWORD=your-app-password
EMAIL_ACCOUNT_1_PROVIDER=gmail

# AI
OPENAI_API_KEY=your-openai-api-key

# Notifications (Optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
EXTERNAL_WEBHOOK_URL=https://webhook.site/...
```

### Email Account Setup

#### Gmail
1. Enable 2-Factor Authentication
2. Generate App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. Use the generated password in `EMAIL_ACCOUNT_X_PASSWORD`

#### Outlook
1. Enable 2-Factor Authentication
2. Generate App Password:
   - Go to Microsoft Account security
   - Advanced security options → App passwords
   - Create new app password
3. Use the generated password in `EMAIL_ACCOUNT_X_PASSWORD`

## Monitoring and Maintenance

### Health Checks

```bash
# Backend health
curl http://localhost:3000/health

# Elasticsearch health
curl http://localhost:9200/_health

# System test
npm run test:system
```

### Logs

```bash
# Application logs
tail -f logs/combined.log

# Error logs
tail -f logs/error.log

# Docker logs
docker-compose logs -f onebox-backend
```

### Performance Monitoring

- Monitor Elasticsearch cluster health
- Track email processing rates
- Monitor AI API usage and costs
- Set up alerts for system failures

## Scaling

### Horizontal Scaling

1. **Load Balancer**: Distribute traffic across multiple backend instances
2. **Database Scaling**: Use Elasticsearch cluster with multiple nodes
3. **Caching**: Implement Redis clustering for high availability

### Vertical Scaling

1. **CPU/Memory**: Increase resources for AI processing
2. **Storage**: Ensure sufficient disk space for email storage
3. **Network**: Optimize for high email throughput

## Troubleshooting

### Common Issues

1. **Elasticsearch Connection Failed**
   - Check if Elasticsearch is running
   - Verify connection URL and credentials
   - Check firewall settings

2. **Email Sync Not Working**
   - Verify email credentials
   - Check IMAP settings
   - Review email provider security settings

3. **AI Features Not Working**
   - Verify OpenAI API key
   - Check API usage limits
   - Review error logs

4. **Frontend Not Loading**
   - Check if backend is running
   - Verify API endpoints
   - Check browser console for errors

### Support

- Check logs in `logs/` directory
- Run system tests: `npm run test:system`
- Review configuration in `.env` file
- Check service status and connectivity

## Security Best Practices

1. **Secrets Management**
   - Use environment variables for sensitive data
   - Never commit secrets to version control
   - Rotate API keys regularly

2. **Network Security**
   - Use HTTPS in production
   - Configure proper firewall rules
   - Limit database access

3. **Email Security**
   - Use app passwords, not main passwords
   - Enable 2FA on email accounts
   - Monitor for suspicious activity

4. **Regular Updates**
   - Keep dependencies updated
   - Apply security patches
   - Monitor for vulnerabilities
