# Onebox Email System

A centralized email aggregator that collects emails from multiple accounts (Gmail, Outlook), analyzes them with AI, and provides a searchable, interactive interface with smart features.

## Features

### ✅ Core Features (Implemented)

- 🔄 **Real-Time Email Synchronization**: IMAP with IDLE mode for live email updates from Gmail and Outlook
- 🔍 **Elasticsearch Integration**: Fast, searchable email storage and retrieval with advanced filtering
- 🤖 **AI-Powered Categorization**: Automatic email classification using OpenAI GPT-4
  - Interested → Potential lead shows interest
  - Meeting Booked → Meeting already scheduled
  - Not Interested → Lead rejected outreach
  - Spam → Junk email detection
  - Out of Office → Automated replies
- 📱 **Slack & Webhook Integration**: Real-time notifications for "Interested" emails
- 💬 **AI-Powered Suggested Replies (RAG)**: Context-aware smart replies using vector database
- 🎯 **Interactive Frontend**: Modern React-based interface with real-time updates
- 📊 **Dashboard & Analytics**: Email statistics and category breakdowns
- 🔍 **Advanced Search**: Full-text search with filters by account, category, date, and status
- ⚡ **Real-time Updates**: Socket.IO for live email notifications
- 🐳 **Docker Support**: Complete containerized deployment setup

## Tech Stack

- **Backend**: Node.js + TypeScript + Express
- **Database**: Elasticsearch (emails) + Redis (caching)
- **Email Protocol**: IMAP with IDLE mode
- **AI**: OpenAI GPT-4 for categorization and reply suggestions
- **Frontend**: React + TypeScript
- **Notifications**: Slack webhooks + Custom webhooks
- **Vector Database**: Pinecone (for RAG)

## Quick Start

### Prerequisites

- Node.js 18+
- Elasticsearch 8.x
- Redis 7.x
- OpenAI API key
- Email accounts with app passwords enabled (Gmail/Outlook)

### Option 1: Automated Setup

```bash
git clone <repository-url>
cd onebox-email-system
npm run setup
```

### Option 2: Manual Setup

1. **Clone and Install**:
```bash
git clone <repository-url>
cd onebox-email-system
npm install
cd frontend && npm install && cd ..
```

2. **Configure Environment**:
```bash
cp .env.example .env
# Edit .env with your configuration (see Configuration section)
```

3. **Start Services** (choose one):

   **Development Mode:**
   ```bash
   # Terminal 1: Start backend
   npm run dev

   # Terminal 2: Start frontend
   cd frontend && npm run dev
   ```

   **Docker Mode:**
   ```bash
   npm run docker:up
   ```

4. **Test the System**:
```bash
npm run test:system
```

### Configuration

#### Email Accounts Setup

1. **Gmail**: Enable 2FA and generate an app password
2. **Outlook**: Enable 2FA and generate an app password

Add your accounts to `.env`:
```env
EMAIL_ACCOUNT_1_EMAIL=<EMAIL>
EMAIL_ACCOUNT_1_PASSWORD=your-app-password
EMAIL_ACCOUNT_1_PROVIDER=gmail

EMAIL_ACCOUNT_2_EMAIL=<EMAIL>
EMAIL_ACCOUNT_2_PASSWORD=your-app-password
EMAIL_ACCOUNT_2_PROVIDER=outlook
```

#### Required Services

1. **Elasticsearch**: Download and run locally or use cloud service
2. **Redis**: Install locally or use cloud service
3. **OpenAI API**: Get API key from OpenAI platform

## Project Structure

```
onebox-email-system/
├── src/
│   ├── config/           # Configuration files
│   ├── services/         # Core business logic
│   ├── routes/           # API routes
│   ├── middleware/       # Express middleware
│   ├── utils/            # Utility functions
│   ├── types/            # TypeScript type definitions
│   └── controllers/      # Route controllers
├── frontend/             # React frontend
├── tests/                # Test files
├── logs/                 # Application logs
└── docs/                 # Documentation
```

## API Endpoints

- `GET /health` - Health check
- `GET /api/emails` - List emails with filtering
- `GET /api/emails/:id` - Get specific email
- `POST /api/search` - Search emails
- `POST /api/ai/categorize` - Manually categorize email
- `POST /api/ai/suggest-reply` - Get suggested reply

## Development

### Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run test` - Run tests
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

### Testing

```bash
npm test
```

## Deployment

1. Build the application:
```bash
npm run build
```

2. Set production environment variables
3. Start the production server:
```bash
npm start
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details
