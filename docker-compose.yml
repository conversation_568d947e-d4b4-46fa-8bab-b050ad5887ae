version: '3.8'

services:
  # Elasticsearch for email storage and search
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: onebox-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - onebox-network

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: onebox-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - onebox-network

  # Onebox Backend
  onebox-backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: onebox-backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - REDIS_URL=redis://redis:6379
    env_file:
      - .env
    depends_on:
      - elasticsearch
      - redis
    volumes:
      - ./logs:/app/logs
    networks:
      - onebox-network
    restart: unless-stopped

  # Onebox Frontend
  onebox-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: onebox-frontend
    ports:
      - "80:80"
    depends_on:
      - onebox-backend
    networks:
      - onebox-network
    restart: unless-stopped

volumes:
  elasticsearch_data:
    driver: local
  redis_data:
    driver: local

networks:
  onebox-network:
    driver: bridge
