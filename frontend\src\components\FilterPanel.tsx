import React, { useState, useEffect } from 'react';
import { FilterPanelProps, AICategory } from '../types';
import { emailService } from '../services/emailService';

const FilterPanel: React.FC<FilterPanelProps> = ({ onFilterChange }) => {
  const [filters, setFilters] = useState({
    category: '' as AICategory | '',
    isRead: undefined as boolean | undefined,
    isStarred: undefined as boolean | undefined,
    accountId: '',
    folder: '',
  });

  const [filterOptions, setFilterOptions] = useState({
    accounts: [] as { id: string; count: number }[],
    categories: [] as { id: string; name: string; count: number }[],
    folders: [] as { id: string; count: number }[],
  });

  useEffect(() => {
    loadFilterOptions();
  }, []);

  const loadFilterOptions = async () => {
    try {
      const options = await emailService.getFilterOptions();
      setFilterOptions(options);
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  };

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);

    // Convert to SearchQuery format
    const searchQuery = {
      category: newFilters.category || undefined,
      isRead: newFilters.isRead,
      isStarred: newFilters.isStarred,
      accountId: newFilters.accountId || undefined,
      folder: newFilters.folder || undefined,
    };

    onFilterChange(searchQuery);
  };

  const clearFilters = () => {
    const clearedFilters = {
      category: '' as AICategory | '',
      isRead: undefined,
      isStarred: undefined,
      accountId: '',
      folder: '',
    };
    setFilters(clearedFilters);
    onFilterChange({});
  };

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Filters</h3>
          <button
            onClick={clearFilters}
            className="text-sm text-primary-600 hover:text-primary-700"
          >
            Clear all
          </button>
        </div>
      </div>
      <div className="card-body space-y-6">
        {/* Category Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Category
          </label>
          <select
            value={filters.category}
            onChange={(e) => handleFilterChange('category', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          >
            <option value="">All categories</option>
            <option value="interested">Interested</option>
            <option value="meeting_booked">Meeting Booked</option>
            <option value="not_interested">Not Interested</option>
            <option value="spam">Spam</option>
            <option value="out_of_office">Out of Office</option>
          </select>
        </div>

        {/* Read Status Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Read Status
          </label>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                name="readStatus"
                checked={filters.isRead === undefined}
                onChange={() => handleFilterChange('isRead', undefined)}
                className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700">All</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="readStatus"
                checked={filters.isRead === false}
                onChange={() => handleFilterChange('isRead', false)}
                className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700">Unread</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="readStatus"
                checked={filters.isRead === true}
                onChange={() => handleFilterChange('isRead', true)}
                className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700">Read</span>
            </label>
          </div>
        </div>

        {/* Starred Filter */}
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={filters.isStarred === true}
              onChange={(e) => handleFilterChange('isStarred', e.target.checked ? true : undefined)}
              className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-700">Starred only</span>
          </label>
        </div>

        {/* Account Filter */}
        {filterOptions.accounts.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Account
            </label>
            <select
              value={filters.accountId}
              onChange={(e) => handleFilterChange('accountId', e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="">All accounts</option>
              {filterOptions.accounts.map((account) => (
                <option key={account.id} value={account.id}>
                  {account.id} ({account.count})
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Folder Filter */}
        {filterOptions.folders.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Folder
            </label>
            <select
              value={filters.folder}
              onChange={(e) => handleFilterChange('folder', e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="">All folders</option>
              {filterOptions.folders.map((folder) => (
                <option key={folder.id} value={folder.id}>
                  {folder.id} ({folder.count})
                </option>
              ))}
            </select>
          </div>
        )}
      </div>
    </div>
  );
};

export default FilterPanel;
