import OpenAI from 'openai';
import { Email, AIAnalysis, AICategory, SuggestedReply } from '@/types';
import { config } from '@/config';
import logger from '@/utils/logger';
import { VectorService } from './VectorService';

export class AIService {
  private openai: OpenAI;
  private vectorService: VectorService;

  constructor() {
    if (!config.openai.apiKey) {
      throw new Error('OpenAI API key is required');
    }

    this.openai = new OpenAI({
      apiKey: config.openai.apiKey,
    });

    this.vectorService = new VectorService();
  }

  public async initialize(): Promise<void> {
    await this.vectorService.initialize();
    logger.info('✅ AI service initialized');
  }

  public async categorizeEmail(email: Email): Promise<AIAnalysis> {
    try {
      logger.info(`Categorizing email: ${email.subject}`);

      const prompt = this.buildCategorizationPrompt(email);
      
      const response = await this.openai.chat.completions.create({
        model: config.openai.model,
        messages: [
          {
            role: 'system',
            content: `You are an AI assistant that categorizes emails for a business outreach system. 
            You must categorize each email into exactly one of these categories:
            - interested: Lead shows interest in the product/service
            - meeting_booked: Meeting or call is already scheduled
            - not_interested: Lead explicitly declines or shows no interest
            - spam: Junk email, promotional content, or irrelevant messages
            - out_of_office: Automated out-of-office replies
            
            Respond with a JSON object containing:
            - category: one of the above categories
            - confidence: number between 0-1
            - reasoning: brief explanation for the categorization
            - extractedInfo: object with meetingTime, contactInfo, urgency if applicable`,
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.1,
        max_tokens: 500,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      const analysis = JSON.parse(content) as AIAnalysis;
      
      // Validate the response
      this.validateAnalysis(analysis);

      logger.info(`Email categorized as: ${analysis.category} (confidence: ${analysis.confidence})`);
      return analysis;

    } catch (error) {
      logger.error(`Error categorizing email ${email.id}:`, error);
      
      // Return a default categorization if AI fails
      return {
        category: 'spam',
        confidence: 0.1,
        reasoning: 'AI categorization failed, defaulting to spam',
      };
    }
  }

  public async suggestReply(email: Email, knowledgeBase?: string[]): Promise<SuggestedReply> {
    try {
      logger.info(`Generating reply suggestion for email: ${email.subject}`);

      // Use vector service for contextual reply generation
      return await this.vectorService.generateContextualReply(email, knowledgeBase);

    } catch (error) {
      logger.error(`Error generating reply suggestion for email ${email.id}:`, error);

      // Return a default reply if AI fails
      return {
        content: 'Thank you for your email. I will review it and get back to you soon.',
        confidence: 0.1,
        sources: [],
        reasoning: 'AI reply generation failed, using default response',
      };
    }
  }

  public async batchCategorizeEmails(emails: Email[]): Promise<Map<string, AIAnalysis>> {
    const results = new Map<string, AIAnalysis>();
    
    // Process emails in batches to avoid rate limits
    const batchSize = 5;
    for (let i = 0; i < emails.length; i += batchSize) {
      const batch = emails.slice(i, i + batchSize);
      
      const promises = batch.map(async (email) => {
        try {
          const analysis = await this.categorizeEmail(email);
          results.set(email.id, analysis);
        } catch (error) {
          logger.error(`Error in batch categorization for email ${email.id}:`, error);
        }
      });

      await Promise.all(promises);
      
      // Add delay between batches to respect rate limits
      if (i + batchSize < emails.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  private buildCategorizationPrompt(email: Email): string {
    return `
Email to categorize:

Subject: ${email.subject}
From: ${email.from.name || ''} <${email.from.address}>
Date: ${email.date.toISOString()}

Body:
${email.body.text || email.body.html || 'No content'}

Please categorize this email and provide your analysis in JSON format.
    `.trim();
  }

  private buildReplySuggestionPrompt(email: Email, knowledgeBase?: string[]): string {
    let prompt = `
Email to reply to:

Subject: ${email.subject}
From: ${email.from.name || ''} <${email.from.address}>
Date: ${email.date.toISOString()}

Body:
${email.body.text || email.body.html || 'No content'}
    `;

    if (knowledgeBase && knowledgeBase.length > 0) {
      prompt += `\n\nKnowledge Base Information:\n${knowledgeBase.join('\n\n')}`;
    }

    prompt += '\n\nPlease generate an appropriate reply and provide your response in JSON format.';

    return prompt.trim();
  }

  private validateAnalysis(analysis: AIAnalysis): void {
    const validCategories: AICategory[] = [
      'interested',
      'meeting_booked',
      'not_interested',
      'spam',
      'out_of_office',
    ];

    if (!validCategories.includes(analysis.category)) {
      throw new Error(`Invalid category: ${analysis.category}`);
    }

    if (typeof analysis.confidence !== 'number' || analysis.confidence < 0 || analysis.confidence > 1) {
      throw new Error(`Invalid confidence score: ${analysis.confidence}`);
    }

    if (!analysis.reasoning || typeof analysis.reasoning !== 'string') {
      throw new Error('Missing or invalid reasoning');
    }
  }

  public async analyzeEmailSentiment(email: Email): Promise<{
    sentiment: 'positive' | 'negative' | 'neutral';
    score: number;
    reasoning: string;
  }> {
    try {
      const prompt = `
Analyze the sentiment of this email:

Subject: ${email.subject}
Body: ${email.body.text || email.body.html || 'No content'}

Respond with JSON containing:
- sentiment: 'positive', 'negative', or 'neutral'
- score: number between -1 (very negative) and 1 (very positive)
- reasoning: brief explanation
      `.trim();

      const response = await this.openai.chat.completions.create({
        model: config.openai.model,
        messages: [
          {
            role: 'system',
            content: 'You are an AI that analyzes email sentiment for business communications.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.1,
        max_tokens: 200,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      return JSON.parse(content);
    } catch (error) {
      logger.error(`Error analyzing sentiment for email ${email.id}:`, error);
      return {
        sentiment: 'neutral',
        score: 0,
        reasoning: 'Sentiment analysis failed',
      };
    }
  }

  public async extractKeyInformation(email: Email): Promise<{
    entities: string[];
    keyPhrases: string[];
    actionItems: string[];
    urgency: 'low' | 'medium' | 'high';
  }> {
    try {
      const prompt = `
Extract key information from this email:

Subject: ${email.subject}
Body: ${email.body.text || email.body.html || 'No content'}

Respond with JSON containing:
- entities: array of important names, companies, dates, etc.
- keyPhrases: array of important phrases or topics
- actionItems: array of tasks or actions mentioned
- urgency: 'low', 'medium', or 'high' based on language and content
      `.trim();

      const response = await this.openai.chat.completions.create({
        model: config.openai.model,
        messages: [
          {
            role: 'system',
            content: 'You are an AI that extracts key information from business emails.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.1,
        max_tokens: 400,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      return JSON.parse(content);
    } catch (error) {
      logger.error(`Error extracting key information from email ${email.id}:`, error);
      return {
        entities: [],
        keyPhrases: [],
        actionItems: [],
        urgency: 'low',
      };
    }
  }
}
