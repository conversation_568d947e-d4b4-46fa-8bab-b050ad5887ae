import { Client } from '@elastic/elasticsearch';
import { Email, SearchQuery, SearchResult, AICategory } from '@/types';
import { config } from '@/config';
import logger from '@/utils/logger';

export class ElasticsearchService {
  private client: Client;
  private readonly emailIndex = 'onebox-emails';

  constructor() {
    const clientConfig: any = {
      node: config.elasticsearch.url,
    };
    
    if (config.elasticsearch.username && config.elasticsearch.password) {
      clientConfig.auth = {
        username: config.elasticsearch.username,
        password: config.elasticsearch.password,
      };
    }
    
    this.client = new Client(clientConfig);
  }

  public async initialize(): Promise<void> {
    try {
      // Check if Elasticsearch is running
      await this.client.ping();
      logger.info('✅ Connected to Elasticsearch');

      // Create email index if it doesn't exist
      await this.createEmailIndex();
      
      logger.info('✅ Elasticsearch service initialized');
    } catch (error) {
      logger.error('❌ Failed to connect to Elasticsearch:', error);
      throw error;
    }
  }

  private async createEmailIndex(): Promise<void> {
    try {
      const indexExists = await this.client.indices.exists({
        index: this.emailIndex,
      });

      if (!indexExists) {
        await this.client.indices.create({
          index: this.emailIndex,
          body: {
            settings: {
              number_of_shards: 1,
              number_of_replicas: 0,
              analysis: {
                analyzer: {
                  email_analyzer: {
                    type: 'custom',
                    tokenizer: 'standard',
                    filter: ['lowercase', 'stop', 'snowball'],
                  },
                },
              },
            },
            mappings: {
              properties: {
                id: { type: 'keyword' },
                messageId: { type: 'keyword' },
                accountId: { type: 'keyword' },
                subject: {
                  type: 'text',
                  analyzer: 'email_analyzer',
                  fields: {
                    keyword: { type: 'keyword' },
                  },
                },
                from: {
                  properties: {
                    name: {
                      type: 'text',
                      analyzer: 'email_analyzer',
                      fields: {
                        keyword: { type: 'keyword' },
                      },
                    },
                    address: { type: 'keyword' },
                  },
                },
                to: {
                  type: 'nested',
                  properties: {
                    name: {
                      type: 'text',
                      analyzer: 'email_analyzer',
                      fields: {
                        keyword: { type: 'keyword' },
                      },
                    },
                    address: { type: 'keyword' },
                  },
                },
                cc: {
                  type: 'nested',
                  properties: {
                    name: {
                      type: 'text',
                      analyzer: 'email_analyzer',
                      fields: {
                        keyword: { type: 'keyword' },
                      },
                    },
                    address: { type: 'keyword' },
                  },
                },
                bcc: {
                  type: 'nested',
                  properties: {
                    name: {
                      type: 'text',
                      analyzer: 'email_analyzer',
                      fields: {
                        keyword: { type: 'keyword' },
                      },
                    },
                    address: { type: 'keyword' },
                  },
                },
                date: { type: 'date' },
                body: {
                  properties: {
                    text: {
                      type: 'text',
                      analyzer: 'email_analyzer',
                    },
                    html: {
                      type: 'text',
                      analyzer: 'email_analyzer',
                    },
                  },
                },
                attachments: {
                  type: 'nested',
                  properties: {
                    filename: { type: 'keyword' },
                    contentType: { type: 'keyword' },
                    size: { type: 'long' },
                    contentId: { type: 'keyword' },
                  },
                },
                folder: { type: 'keyword' },
                flags: { type: 'keyword' },
                uid: { type: 'long' },
                aiCategory: { type: 'keyword' },
                isRead: { type: 'boolean' },
                isStarred: { type: 'boolean' },
                createdAt: { type: 'date' },
                updatedAt: { type: 'date' },
              },
            },
          },
        });

        logger.info(`✅ Created Elasticsearch index: ${this.emailIndex}`);
      } else {
        logger.info(`✅ Elasticsearch index already exists: ${this.emailIndex}`);
      }
    } catch (error) {
      logger.error('❌ Error creating Elasticsearch index:', error);
      throw error;
    }
  }

  public async indexEmail(email: Email): Promise<void> {
    try {
      await this.client.index({
        index: this.emailIndex,
        id: email.id,
        body: email,
      });

      logger.debug(`✅ Indexed email: ${email.id}`);
    } catch (error) {
      logger.error(`❌ Error indexing email ${email.id}:`, error);
      throw error;
    }
  }

  public async updateEmail(emailId: string, updates: Partial<Email>): Promise<void> {
    try {
      await this.client.update({
        index: this.emailIndex,
        id: emailId,
        body: {
          doc: {
            ...updates,
            updatedAt: new Date(),
          },
        },
      });

      logger.debug(`✅ Updated email: ${emailId}`);
    } catch (error) {
      logger.error(`❌ Error updating email ${emailId}:`, error);
      throw error;
    }
  }

  public async getEmail(emailId: string): Promise<Email | null> {
    try {
      const response = await this.client.get({
        index: this.emailIndex,
        id: emailId,
      });

      return response._source as Email;
    } catch (error: any) {
      if (error.statusCode === 404) {
        return null;
      }
      logger.error(`❌ Error getting email ${emailId}:`, error);
      throw error;
    }
  }

  public async searchEmails(query: SearchQuery): Promise<SearchResult> {
    try {
      const searchBody: any = {
        query: {
          bool: {
            must: [],
            filter: [],
          },
        },
        sort: [{ date: { order: 'desc' } }],
        from: query.offset || 0,
        size: query.limit || 20,
      };

      // Text search across subject and body
      if (query.query) {
        searchBody.query.bool.must.push({
          multi_match: {
            query: query.query,
            fields: [
              'subject^2',
              'body.text',
              'body.html',
              'from.name',
              'from.address',
            ],
            type: 'best_fields',
            fuzziness: 'AUTO',
          },
        });
      }

      // Filter by account
      if (query.accountId) {
        searchBody.query.bool.filter.push({
          term: { accountId: query.accountId },
        });
      }

      // Filter by folder
      if (query.folder) {
        searchBody.query.bool.filter.push({
          term: { folder: query.folder },
        });
      }

      // Filter by AI category
      if (query.category) {
        searchBody.query.bool.filter.push({
          term: { aiCategory: query.category },
        });
      }

      // Filter by read status
      if (query.isRead !== undefined) {
        searchBody.query.bool.filter.push({
          term: { isRead: query.isRead },
        });
      }

      // Filter by starred status
      if (query.isStarred !== undefined) {
        searchBody.query.bool.filter.push({
          term: { isStarred: query.isStarred },
        });
      }

      // Date range filter
      if (query.dateFrom || query.dateTo) {
        const dateRange: any = {};
        if (query.dateFrom) dateRange.gte = query.dateFrom;
        if (query.dateTo) dateRange.lte = query.dateTo;

        searchBody.query.bool.filter.push({
          range: { date: dateRange },
        });
      }

      // If no conditions, match all
      if (searchBody.query.bool.must.length === 0 && searchBody.query.bool.filter.length === 0) {
        searchBody.query = { match_all: {} };
      }

      const response = await this.client.search({
        index: this.emailIndex,
        body: searchBody,
      });

      const emails = response.hits.hits.map((hit: any) => hit._source as Email);
      const total = typeof response.hits.total === 'number' 
        ? response.hits.total 
        : response.hits.total?.value || 0;

      return {
        emails,
        total,
        took: response.took || 0,
      };
    } catch (error) {
      logger.error('❌ Error searching emails:', error);
      throw error;
    }
  }

  public async getEmailsByCategory(category: AICategory, limit: number = 10): Promise<Email[]> {
    try {
      const response = await this.client.search({
        index: this.emailIndex,
        body: {
          query: {
            term: { aiCategory: category },
          },
          sort: [{ date: { order: 'desc' } }],
          size: limit,
        },
      });

      return response.hits.hits.map((hit: any) => hit._source as Email);
    } catch (error) {
      logger.error(`❌ Error getting emails by category ${category}:`, error);
      throw error;
    }
  }

  public async getEmailStats(): Promise<any> {
    try {
      const response = await this.client.search({
        index: this.emailIndex,
        body: {
          size: 0,
          aggs: {
            total_emails: {
              value_count: { field: 'id' },
            },
            by_account: {
              terms: { field: 'accountId' },
            },
            by_category: {
              terms: { field: 'aiCategory' },
            },
            by_folder: {
              terms: { field: 'folder' },
            },
            unread_count: {
              filter: { term: { isRead: false } },
            },
            starred_count: {
              filter: { term: { isStarred: true } },
            },
            recent_emails: {
              date_histogram: {
                field: 'date',
                calendar_interval: 'day',
                min_doc_count: 0,
                extended_bounds: {
                  min: 'now-30d/d',
                  max: 'now/d',
                },
              },
            },
          },
        },
      });

      return response.aggregations;
    } catch (error) {
      logger.error('❌ Error getting email stats:', error);
      throw error;
    }
  }

  public async deleteEmail(emailId: string): Promise<void> {
    try {
      await this.client.delete({
        index: this.emailIndex,
        id: emailId,
      });

      logger.debug(`✅ Deleted email: ${emailId}`);
    } catch (error) {
      logger.error(`❌ Error deleting email ${emailId}:`, error);
      throw error;
    }
  }

  public async bulkIndexEmails(emails: Email[]): Promise<void> {
    try {
      const body = emails.flatMap(email => [
        { index: { _index: this.emailIndex, _id: email.id } },
        email,
      ]);

      const response = await this.client.bulk({ body });

      if (response.errors) {
        logger.error('❌ Bulk indexing errors:', response.items);
      } else {
        logger.info(`✅ Bulk indexed ${emails.length} emails`);
      }
    } catch (error) {
      logger.error('❌ Error bulk indexing emails:', error);
      throw error;
    }
  }

  public async refreshIndex(): Promise<void> {
    try {
      await this.client.indices.refresh({
        index: this.emailIndex,
      });
    } catch (error) {
      logger.error('❌ Error refreshing index:', error);
      throw error;
    }
  }
}
