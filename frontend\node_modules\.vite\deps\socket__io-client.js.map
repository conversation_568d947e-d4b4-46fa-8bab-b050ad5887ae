{"version": 3, "file": "socket__io-client.js", "names": ["withNativeBlob", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "lookup", "decode", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decode", "isBinary", "protocol", "on", "globalThis", "_a", "globalThis", "globalThis", "value", "protocol", "Socket", "DEFAULT_TRANSPORTS", "_a", "protocol", "Socket", "RESERVED_EVENTS", "value", "self", "parser", "Engine", "self"], "sources": ["../../engine.io-parser/build/esm/commons.js", "../../engine.io-parser/build/esm/encodePacket.browser.js", "../../engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../../engine.io-parser/build/esm/decodePacket.browser.js", "../../engine.io-parser/build/esm/index.js", "../../@socket.io/component-emitter/lib/esm/index.js", "../../engine.io-client/build/esm/globals.js", "../../engine.io-client/build/esm/util.js", "../../engine.io-client/build/esm/contrib/parseqs.js", "../../engine.io-client/build/esm/transport.js", "../../engine.io-client/build/esm/transports/polling.js", "../../engine.io-client/build/esm/contrib/has-cors.js", "../../engine.io-client/build/esm/transports/polling-xhr.js", "../../engine.io-client/build/esm/transports/websocket.js", "../../engine.io-client/build/esm/transports/webtransport.js", "../../engine.io-client/build/esm/transports/index.js", "../../engine.io-client/build/esm/contrib/parseuri.js", "../../engine.io-client/build/esm/socket.js", "../../engine.io-client/build/esm/transports/polling-fetch.js", "../../engine.io-client/build/esm/index.js", "../../socket.io-client/build/esm/url.js", "../../socket.io-parser/build/esm/is-binary.js", "../../socket.io-parser/build/esm/binary.js", "../../socket.io-parser/build/esm/index.js", "../../socket.io-client/build/esm/on.js", "../../socket.io-client/build/esm/socket.js", "../../socket.io-client/build/esm/contrib/backo2.js", "../../socket.io-client/build/esm/manager.js", "../../socket.io-client/build/esm/index.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType),\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* State.READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\nexport const defaultBinaryType = \"arraybuffer\";\nexport function createCookieJar() { }\n", "import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nexport class Polling extends Transport {\n    constructor() {\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    _poll() {\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nfunction empty() { }\nexport class BaseXHR extends Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n        super();\n        this.createRequest = createRequest;\n        installTimerFunctions(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n        var _a;\n        const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = (this._xhr = this.createRequest(opts));\n        try {\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this._opts.extraHeaders) {\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n                    // @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this._data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = (function () {\n    const xhr = newRequest({\n        xdomain: false,\n    });\n    return xhr && xhr.responseType !== null;\n})();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    constructor(opts) {\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = () => { };\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative\n            ? protocols\n                ? new WebSocketCtor(uri, protocols)\n                : new WebSocketCtor(uri)\n            : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"../globals.node.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(() => {\n            this._transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "import { XHR } from \"./polling-xhr.node.js\";\nimport { WS } from \"./websocket.node.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: XHR,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick, } from \"./globals.node.js\";\nconst withEventListeners = typeof addEventListener === \"function\" &&\n    typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", () => {\n        OFFLINE_EVENT_LISTENERS.forEach((listener) => listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\nexport class SocketWithoutUpgrade extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */\n        this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = parse(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure =\n                parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query)\n                opts.query = parsedUri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t) => {\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this._offlineEventListener = () => {\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = createCookieJar();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade &&\n            SocketWithoutUpgrade.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1\n            ? \"websocket\"\n            : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this._onDrain.bind(this))\n            .on(\"packet\", this._onPacket.bind(this))\n            .on(\"error\", this._onError.bind(this))\n            .on(\"close\", (reason) => this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess =\n            \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    _onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(() => {\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */\n    /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime)\n            return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            this._pingTimeoutTime = 0;\n            nextTick(() => {\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this._onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    _onError(err) {\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports &&\n            this.transports.length > 1 &&\n            this.readyState === \"opening\") {\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    _onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor() {\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            for (let i = 0; i < this._upgrades.length; i++) {\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    _probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess =\n                        \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for (let i = 0; i < upgrades.length; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\nexport class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}) {\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports ||\n            (o.transports && typeof o.transports[0] === \"string\")) {\n            o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"])\n                .map((transportName) => DEFAULT_TRANSPORTS[transportName])\n                .filter((t) => !!t);\n        }\n        super(uri, o);\n    }\n}\n", "import { Polling } from \"./polling.js\";\n/**\n * HTTP long-polling based on the built-in `fetch()` method.\n *\n * Usage: browser, Node.js (since v18), <PERSON><PERSON>, <PERSON>un\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/fetch\n * @see https://caniuse.com/fetch\n * @see https://nodejs.org/api/globals.html#fetch\n */\nexport class Fetch extends Polling {\n    doPoll() {\n        this._fetch()\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch read error\", res.status, res);\n            }\n            res.text().then((data) => this.onData(data));\n        })\n            .catch((err) => {\n            this.onError(\"fetch read error\", err);\n        });\n    }\n    doWrite(data, callback) {\n        this._fetch(data)\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch write error\", res.status, res);\n            }\n            callback();\n        })\n            .catch((err) => {\n            this.onError(\"fetch write error\", err);\n        });\n    }\n    _fetch(data) {\n        var _a;\n        const isPost = data !== undefined;\n        const headers = new Headers(this.opts.extraHeaders);\n        if (isPost) {\n            headers.set(\"content-type\", \"text/plain;charset=UTF-8\");\n        }\n        (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.appendCookies(headers);\n        return fetch(this.uri(), {\n            method: isPost ? \"POST\" : \"GET\",\n            body: isPost ? data : null,\n            headers,\n            credentials: this.opts.withCredentials ? \"include\" : \"omit\",\n        }).then((res) => {\n            var _a;\n            // @ts-ignore getSetCookie() was added in Node.js v19.7.0\n            (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(res.headers.getSetCookie());\n            return res;\n        });\n    }\n}\n", "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport { SocketWithoutUpgrade, SocketWithUpgrade, } from \"./socket.js\";\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./globals.node.js\";\nexport { Fetch } from \"./transports/polling-fetch.js\";\nexport { XHR as NodeXHR } from \"./transports/polling-xhr.node.js\";\nexport { XHR } from \"./transports/polling-xhr.js\";\nexport { WS as NodeWebSocket } from \"./transports/websocket.node.js\";\nexport { WS as WebSocket } from \"./transports/websocket.js\";\nexport { WT as WebTransport } from \"./transports/webtransport.js\";\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\n    \"connect\",\n    \"connect_error\",\n    \"disconnect\",\n    \"disconnecting\",\n    \"newListener\",\n    \"removeListener\", // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return (Array.isArray(payload) &&\n                    (typeof payload[0] === \"number\" ||\n                        (typeof payload[0] === \"string\" &&\n                            RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n        }\n        else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject) => {\n            const fn = (arg1, arg2) => {\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */\n    _clearAcks() {\n        Object.keys(this.acks).forEach((id) => {\n            const isBuffered = this.sendBuffer.some((packet) => String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            return;\n        }\n        delete this.acks[packet.id];\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        if (!v) {\n            this.skipReconnect = true;\n        }\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), \n        // @ts-ignore\n        on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called when:\n     *\n     * - the low-level engine is closed\n     * - the parser encountered a badly formatted packet\n     * - all sockets are disconnected\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        var _a;\n        this.cleanup();\n        (_a = this.engine) === null || _a === void 0 ? void 0 : _a.close();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\nexport { Fetch, NodeXHR, XHR, NodeWebSocket, WebSocket, WebTransport, } from \"engine.io-client\";\n"], "mappings": ";;;AAAA,IAAM,eAAe,OAAO,OAAO,KAAK;AACxC,aAAa,UAAU;AACvB,aAAa,WAAW;AACxB,aAAa,UAAU;AACvB,aAAa,UAAU;AACvB,aAAa,aAAa;AAC1B,aAAa,aAAa;AAC1B,aAAa,UAAU;AACvB,IAAM,uBAAuB,OAAO,OAAO,KAAK;AAChD,OAAO,KAAK,aAAa,CAAC,SAAS,QAAQ;AACvC,sBAAqB,aAAa,QAAQ;EAC5C;AACF,IAAM,eAAe;CAAE,MAAM;CAAS,MAAM;CAAgB;;;;ACX5D,IAAMA,mBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,OAAO,UAAU,SAAS,KAAK,KAAK,KAAK;AACjD,IAAMC,0BAAwB,OAAO,gBAAgB;AAErD,IAAMC,YAAU,QAAQ;AACpB,QAAO,OAAO,YAAY,WAAW,aAC/B,YAAY,OAAO,IAAI,GACvB,OAAO,IAAI,kBAAkB;;AAEvC,IAAM,gBAAgB,EAAE,MAAM,QAAQ,gBAAgB,aAAa;AAC/D,KAAIF,oBAAkB,gBAAgB,KAClC,KAAI,eACA,QAAO,SAAS,KAAK;KAGrB,QAAO,mBAAmB,MAAM,SAAS;UAGxCC,4BACJ,gBAAgB,eAAeC,SAAO,KAAK,EAC5C,KAAI,eACA,QAAO,SAAS,KAAK;KAGrB,QAAO,mBAAmB,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,SAAS;AAI7D,QAAO,SAAS,aAAa,SAAS,QAAQ,IAAI;;AAEtD,IAAM,sBAAsB,MAAM,aAAa;CAC3C,MAAM,aAAa,IAAI,YAAY;AACnC,YAAW,SAAS,WAAY;EAC5B,MAAM,UAAU,WAAW,OAAO,MAAM,IAAI,CAAC;AAC7C,WAAS,OAAO,WAAW,IAAI;;AAEnC,QAAO,WAAW,cAAc,KAAK;;AAEzC,SAAS,QAAQ,MAAM;AACnB,KAAI,gBAAgB,WAChB,QAAO;UAEF,gBAAgB,YACrB,QAAO,IAAI,WAAW,KAAK;KAG3B,QAAO,IAAI,WAAW,KAAK,QAAQ,KAAK,YAAY,KAAK,WAAW;;AAG5E,IAAI;AACJ,SAAgB,qBAAqB,QAAQ,UAAU;AACnD,KAAIF,oBAAkB,OAAO,gBAAgB,KACzC,QAAO,OAAO,KAAK,aAAa,CAAC,KAAK,QAAQ,CAAC,KAAK,SAAS;UAExDC,4BACJ,OAAO,gBAAgB,eAAeC,SAAO,OAAO,KAAK,EAC1D,QAAO,SAAS,QAAQ,OAAO,KAAK,CAAC;AAEzC,cAAa,QAAQ,QAAQ,YAAY;AACrC,MAAI,CAAC,aACD,gBAAe,IAAI,aAAa;AAEpC,WAAS,aAAa,OAAO,QAAQ,CAAC;GACxC;;;;;AChEN,IAAM,QAAQ;AAEd,IAAMC,WAAS,OAAO,eAAe,cAAc,EAAE,GAAG,IAAI,WAAW,IAAI;AAC3E,KAAK,IAAI,IAAI,GAAG,IAAI,IAAc,IAC9B,UAAO,MAAM,WAAW,EAAE,IAAI;AAkBlC,MAAaC,YAAU,WAAW;CAC9B,IAAI,eAAe,OAAO,SAAS,KAAM,MAAM,OAAO,QAAQ,GAAG,IAAI,GAAG,UAAU,UAAU,UAAU;AACtG,KAAI,OAAO,OAAO,SAAS,OAAO,KAAK;AACnC;AACA,MAAI,OAAO,OAAO,SAAS,OAAO,IAC9B;;CAGR,MAAM,cAAc,IAAI,YAAY,aAAa,EAAE,QAAQ,IAAI,WAAW,YAAY;AACtF,MAAK,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AACzB,aAAWD,SAAO,OAAO,WAAW,EAAE;AACtC,aAAWA,SAAO,OAAO,WAAW,IAAI,EAAE;AAC1C,aAAWA,SAAO,OAAO,WAAW,IAAI,EAAE;AAC1C,aAAWA,SAAO,OAAO,WAAW,IAAI,EAAE;AAC1C,QAAM,OAAQ,YAAY,IAAM,YAAY;AAC5C,QAAM,QAAS,WAAW,OAAO,IAAM,YAAY;AACnD,QAAM,QAAS,WAAW,MAAM,IAAM,WAAW;;AAErD,QAAO;;;;;ACvCX,IAAME,0BAAwB,OAAO,gBAAgB;AACrD,MAAa,gBAAgB,eAAe,eAAe;AACvD,KAAI,OAAO,kBAAkB,SACzB,QAAO;EACH,MAAM;EACN,MAAM,UAAU,eAAe,WAAW;EAC7C;CAEL,MAAM,OAAO,cAAc,OAAO,EAAE;AACpC,KAAI,SAAS,IACT,QAAO;EACH,MAAM;EACN,MAAM,mBAAmB,cAAc,UAAU,EAAE,EAAE,WAAW;EACnE;AAGL,KAAI,CADe,qBAAqB,MAEpC,QAAO;AAEX,QAAO,cAAc,SAAS,IACxB;EACE,MAAM,qBAAqB;EAC3B,MAAM,cAAc,UAAU,EAAE;EACnC,GACC,EACE,MAAM,qBAAqB,OAC9B;;AAET,IAAM,sBAAsB,MAAM,eAAe;AAC7C,KAAIA,yBAAuB;EACvB,MAAM,UAAUC,SAAO,KAAK;AAC5B,SAAO,UAAU,SAAS,WAAW;OAGrC,QAAO;EAAE,QAAQ;EAAM;EAAM;;AAGrC,IAAM,aAAa,MAAM,eAAe;AACpC,SAAQ,YAAR;EACI,KAAK,OACD,KAAI,gBAAgB,KAEhB,QAAO;MAIP,QAAO,IAAI,KAAK,CAAC,KAAK,CAAC;EAE/B,KAAK;EACL,QACI,KAAI,gBAAgB,YAEhB,QAAO;MAIP,QAAO,KAAK;;;;;;ACvD5B,IAAM,YAAY,OAAO,aAAa,GAAG;AACzC,IAAM,iBAAiB,SAAS,aAAa;CAEzC,MAAM,SAAS,QAAQ;CACvB,MAAM,iBAAiB,IAAI,MAAM,OAAO;CACxC,IAAI,QAAQ;AACZ,SAAQ,SAAS,QAAQ,MAAM;AAE3B,eAAa,QAAQ,QAAQ,kBAAkB;AAC3C,kBAAe,KAAK;AACpB,OAAI,EAAE,UAAU,OACZ,UAAS,eAAe,KAAK,UAAU,CAAC;IAE9C;GACJ;;AAEN,IAAM,iBAAiB,gBAAgB,eAAe;CAClD,MAAM,iBAAiB,eAAe,MAAM,UAAU;CACtD,MAAM,UAAU,EAAE;AAClB,MAAK,IAAI,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;EAC5C,MAAM,gBAAgB,aAAa,eAAe,IAAI,WAAW;AACjE,UAAQ,KAAK,cAAc;AAC3B,MAAI,cAAc,SAAS,QACvB;;AAGR,QAAO;;AAEX,SAAgB,4BAA4B;AACxC,QAAO,IAAI,gBAAgB,EACvB,UAAU,QAAQ,YAAY;AAC1B,uBAAqB,SAAS,kBAAkB;GAC5C,MAAM,gBAAgB,cAAc;GACpC,IAAI;AAEJ,OAAI,gBAAgB,KAAK;AACrB,aAAS,IAAI,WAAW,EAAE;AAC1B,QAAI,SAAS,OAAO,OAAO,CAAC,SAAS,GAAG,cAAc;cAEjD,gBAAgB,OAAO;AAC5B,aAAS,IAAI,WAAW,EAAE;IAC1B,MAAM,OAAO,IAAI,SAAS,OAAO,OAAO;AACxC,SAAK,SAAS,GAAG,IAAI;AACrB,SAAK,UAAU,GAAG,cAAc;UAE/B;AACD,aAAS,IAAI,WAAW,EAAE;IAC1B,MAAM,OAAO,IAAI,SAAS,OAAO,OAAO;AACxC,SAAK,SAAS,GAAG,IAAI;AACrB,SAAK,aAAa,GAAG,OAAO,cAAc,CAAC;;AAG/C,OAAI,OAAO,QAAQ,OAAO,OAAO,SAAS,SACtC,QAAO,MAAM;AAEjB,cAAW,QAAQ,OAAO;AAC1B,cAAW,QAAQ,cAAc;IACnC;IAET,CAAC;;AAEN,IAAI;AACJ,SAAS,YAAY,QAAQ;AACzB,QAAO,OAAO,QAAQ,KAAK,UAAU,MAAM,MAAM,QAAQ,EAAE;;AAE/D,SAAS,aAAa,QAAQ,MAAM;AAChC,KAAI,OAAO,GAAG,WAAW,KACrB,QAAO,OAAO,OAAO;CAEzB,MAAM,SAAS,IAAI,WAAW,KAAK;CACnC,IAAI,IAAI;AACR,MAAK,IAAI,IAAI,GAAG,IAAI,MAAM,KAAK;AAC3B,SAAO,KAAK,OAAO,GAAG;AACtB,MAAI,MAAM,OAAO,GAAG,QAAQ;AACxB,UAAO,OAAO;AACd,OAAI;;;AAGZ,KAAI,OAAO,UAAU,IAAI,OAAO,GAAG,OAC/B,QAAO,KAAK,OAAO,GAAG,MAAM,EAAE;AAElC,QAAO;;AAEX,SAAgB,0BAA0B,YAAY,YAAY;AAC9D,KAAI,CAAC,aACD,gBAAe,IAAI,aAAa;CAEpC,MAAM,SAAS,EAAE;CACjB,IAAI,QAAQ;CACZ,IAAI,iBAAiB;CACrB,IAAIC,aAAW;AACf,QAAO,IAAI,gBAAgB,EACvB,UAAU,OAAO,YAAY;AACzB,SAAO,KAAK,MAAM;AAClB,SAAO,MAAM;AACT,OAAI,UAAU,GAA2B;AACrC,QAAI,YAAY,OAAO,GAAG,EACtB;IAEJ,MAAM,SAAS,aAAa,QAAQ,EAAE;AACtC,kBAAY,OAAO,KAAK,SAAU;AAClC,qBAAiB,OAAO,KAAK;AAC7B,QAAI,iBAAiB,IACjB,SAAQ;aAEH,mBAAmB,IACxB,SAAQ;QAGR,SAAQ;cAGP,UAAU,GAAuC;AACtD,QAAI,YAAY,OAAO,GAAG,EACtB;IAEJ,MAAM,cAAc,aAAa,QAAQ,EAAE;AAC3C,qBAAiB,IAAI,SAAS,YAAY,QAAQ,YAAY,YAAY,YAAY,OAAO,CAAC,UAAU,EAAE;AAC1G,YAAQ;cAEH,UAAU,GAAuC;AACtD,QAAI,YAAY,OAAO,GAAG,EACtB;IAEJ,MAAM,cAAc,aAAa,QAAQ,EAAE;IAC3C,MAAM,OAAO,IAAI,SAAS,YAAY,QAAQ,YAAY,YAAY,YAAY,OAAO;IACzF,MAAM,IAAI,KAAK,UAAU,EAAE;AAC3B,QAAI,IAAI,KAAK,IAAI,GAAG,GAAQ,GAAG,GAAG;AAE9B,gBAAW,QAAQ,aAAa;AAChC;;AAEJ,qBAAiB,IAAI,KAAK,IAAI,GAAG,GAAG,GAAG,KAAK,UAAU,EAAE;AACxD,YAAQ;UAEP;AACD,QAAI,YAAY,OAAO,GAAG,eACtB;IAEJ,MAAM,OAAO,aAAa,QAAQ,eAAe;AACjD,eAAW,QAAQ,aAAaA,aAAW,OAAO,aAAa,OAAO,KAAK,EAAE,WAAW,CAAC;AACzF,YAAQ;;AAEZ,OAAI,mBAAmB,KAAK,iBAAiB,YAAY;AACrD,eAAW,QAAQ,aAAa;AAChC;;;IAIf,CAAC;;AAEN,MAAaC,aAAW;;;;;;;;;ACpJxB,SAAgB,QAAQ,KAAK;AAC3B,KAAI,IAAK,QAAO,MAAM,IAAI;;;;;;;;;AAW5B,SAAS,MAAM,KAAK;AAClB,MAAK,IAAI,OAAO,QAAQ,UACtB,KAAI,OAAO,QAAQ,UAAU;AAE/B,QAAO;;;;;;;;;;AAYT,QAAQ,UAAU,KAClB,QAAQ,UAAU,mBAAmB,SAAS,OAAO,IAAG;AACtD,MAAK,aAAa,KAAK,cAAc,EAAE;AACvC,EAAC,KAAK,WAAW,MAAM,SAAS,KAAK,WAAW,MAAM,UAAU,EAAE,EAC/D,KAAK,GAAG;AACX,QAAO;;;;;;;;;;;AAaT,QAAQ,UAAU,OAAO,SAAS,OAAO,IAAG;CAC1C,SAASC,OAAK;AACZ,OAAK,IAAI,OAAOA,KAAG;AACnB,KAAG,MAAM,MAAM,UAAU;;AAG3B,MAAG,KAAK;AACR,MAAK,GAAG,OAAOA,KAAG;AAClB,QAAO;;;;;;;;;;;AAaT,QAAQ,UAAU,MAClB,QAAQ,UAAU,iBAClB,QAAQ,UAAU,qBAClB,QAAQ,UAAU,sBAAsB,SAAS,OAAO,IAAG;AACzD,MAAK,aAAa,KAAK,cAAc,EAAE;AAGvC,KAAI,KAAK,UAAU,QAAQ;AACzB,OAAK,aAAa,EAAE;AACpB,SAAO;;CAIT,IAAI,YAAY,KAAK,WAAW,MAAM;AACtC,KAAI,CAAC,UAAW,QAAO;AAGvB,KAAI,KAAK,UAAU,QAAQ;AACzB,SAAO,KAAK,WAAW,MAAM;AAC7B,SAAO;;CAIT,IAAI;AACJ,MAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,OAAK,UAAU;AACf,MAAI,OAAO,MAAM,GAAG,OAAO,IAAI;AAC7B,aAAU,OAAO,GAAG,EAAE;AACtB;;;AAMJ,KAAI,UAAU,WAAW,EACvB,QAAO,KAAK,WAAW,MAAM;AAG/B,QAAO;;;;;;;;;AAWT,QAAQ,UAAU,OAAO,SAAS,OAAM;AACtC,MAAK,aAAa,KAAK,cAAc,EAAE;CAEvC,IAAI,OAAO,IAAI,MAAM,UAAU,SAAS,EAAE,EACtC,YAAY,KAAK,WAAW,MAAM;AAEtC,MAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,IACpC,MAAK,IAAI,KAAK,UAAU;AAG1B,KAAI,WAAW;AACb,cAAY,UAAU,MAAM,EAAE;AAC9B,OAAK,IAAI,IAAI,GAAG,MAAM,UAAU,QAAQ,IAAI,KAAK,EAAE,EACjD,WAAU,GAAG,MAAM,MAAM,KAAK;;AAIlC,QAAO;;AAIT,QAAQ,UAAU,eAAe,QAAQ,UAAU;;;;;;;;AAUnD,QAAQ,UAAU,YAAY,SAAS,OAAM;AAC3C,MAAK,aAAa,KAAK,cAAc,EAAE;AACvC,QAAO,KAAK,WAAW,MAAM,UAAU,EAAE;;;;;;;;;AAW3C,QAAQ,UAAU,eAAe,SAAS,OAAM;AAC9C,QAAO,CAAC,CAAE,KAAK,UAAU,MAAM,CAAC;;;;;ACvKlC,MAAa,kBAAkB;AAE3B,KAD2B,OAAO,YAAY,cAAc,OAAO,QAAQ,YAAY,WAEnF,SAAQ,OAAO,QAAQ,SAAS,CAAC,KAAK,GAAG;KAGzC,SAAQ,IAAI,iBAAiB,aAAa,IAAI,EAAE;IAEpD;AACJ,MAAa,wBAAwB;AACjC,KAAI,OAAO,SAAS,YAChB,QAAO;UAEF,OAAO,WAAW,YACvB,QAAO;KAGP,QAAO,SAAS,cAAc,EAAE;IAEpC;AACJ,MAAa,oBAAoB;AACjC,SAAgB,kBAAkB;;;;ACpBlC,SAAgB,KAAK,KAAK,GAAG,MAAM;AAC/B,QAAO,KAAK,QAAQ,KAAK,MAAM;AAC3B,MAAI,IAAI,eAAe,EAAE,CACrB,KAAI,KAAK,IAAI;AAEjB,SAAO;IACR,EAAE,CAAC;;AAGV,IAAM,qBAAqBC,eAAW;AACtC,IAAM,uBAAuBA,eAAW;AACxC,SAAgB,sBAAsB,KAAK,MAAM;AAC7C,KAAI,KAAK,iBAAiB;AACtB,MAAI,eAAe,mBAAmB,KAAKA,eAAW;AACtD,MAAI,iBAAiB,qBAAqB,KAAKA,eAAW;QAEzD;AACD,MAAI,eAAeA,eAAW,WAAW,KAAKA,eAAW;AACzD,MAAI,iBAAiBA,eAAW,aAAa,KAAKA,eAAW;;;AAIrE,IAAM,kBAAkB;AAExB,SAAgB,WAAW,KAAK;AAC5B,KAAI,OAAO,QAAQ,SACf,QAAO,WAAW,IAAI;AAG1B,QAAO,KAAK,MAAM,IAAI,cAAc,IAAI,QAAQ,gBAAgB;;AAEpE,SAAS,WAAW,KAAK;CACrB,IAAI,IAAI,GAAG,SAAS;AACpB,MAAK,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AACxC,MAAI,IAAI,WAAW,EAAE;AACrB,MAAI,IAAI,IACJ,WAAU;WAEL,IAAI,KACT,WAAU;WAEL,IAAI,SAAU,KAAK,MACxB,WAAU;OAET;AACD;AACA,aAAU;;;AAGlB,QAAO;;;;;AAKX,SAAgB,eAAe;AAC3B,QAAQ,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,UAAU,EAAE,GACxC,KAAK,QAAQ,CAAC,SAAS,GAAG,CAAC,UAAU,GAAG,EAAE;;;;;;;;;;;;ACjDlD,SAAgB,OAAO,KAAK;CACxB,IAAI,MAAM;AACV,MAAK,IAAI,KAAK,IACV,KAAI,IAAI,eAAe,EAAE,EAAE;AACvB,MAAI,IAAI,OACJ,QAAO;AACX,SAAO,mBAAmB,EAAE,GAAG,MAAM,mBAAmB,IAAI,GAAG;;AAGvE,QAAO;;;;;;;;AAQX,SAAgB,OAAO,IAAI;CACvB,IAAI,MAAM,EAAE;CACZ,IAAI,QAAQ,GAAG,MAAM,IAAI;AACzB,MAAK,IAAI,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;EAC1C,IAAI,OAAO,MAAM,GAAG,MAAM,IAAI;AAC9B,MAAI,mBAAmB,KAAK,GAAG,IAAI,mBAAmB,KAAK,GAAG;;AAElE,QAAO;;;;;AC5BX,IAAa,iBAAb,cAAoC,MAAM;CACtC,YAAY,QAAQ,aAAa,SAAS;AACtC,QAAM,OAAO;AACb,OAAK,cAAc;AACnB,OAAK,UAAU;AACf,OAAK,OAAO;;;AAGpB,IAAa,YAAb,cAA+B,QAAQ;;;;;;;CAOnC,YAAY,MAAM;AACd,SAAO;AACP,OAAK,WAAW;AAChB,wBAAsB,MAAM,KAAK;AACjC,OAAK,OAAO;AACZ,OAAK,QAAQ,KAAK;AAClB,OAAK,SAAS,KAAK;AACnB,OAAK,iBAAiB,CAAC,KAAK;;;;;;;;;;;CAWhC,QAAQ,QAAQ,aAAa,SAAS;AAClC,QAAM,aAAa,SAAS,IAAI,eAAe,QAAQ,aAAa,QAAQ,CAAC;AAC7E,SAAO;;;;;CAKX,OAAO;AACH,OAAK,aAAa;AAClB,OAAK,QAAQ;AACb,SAAO;;;;;CAKX,QAAQ;AACJ,MAAI,KAAK,eAAe,aAAa,KAAK,eAAe,QAAQ;AAC7D,QAAK,SAAS;AACd,QAAK,SAAS;;AAElB,SAAO;;;;;;;CAOX,KAAK,SAAS;AACV,MAAI,KAAK,eAAe,OACpB,MAAK,MAAM,QAAQ;;;;;;;CAW3B,SAAS;AACL,OAAK,aAAa;AAClB,OAAK,WAAW;AAChB,QAAM,aAAa,OAAO;;;;;;;;CAQ9B,OAAO,MAAM;EACT,MAAM,SAAS,aAAa,MAAM,KAAK,OAAO,WAAW;AACzD,OAAK,SAAS,OAAO;;;;;;;CAOzB,SAAS,QAAQ;AACb,QAAM,aAAa,UAAU,OAAO;;;;;;;CAOxC,QAAQ,SAAS;AACb,OAAK,aAAa;AAClB,QAAM,aAAa,SAAS,QAAQ;;;;;;;CAOxC,MAAM,SAAS;CACf,UAAU,QAAQ,QAAQ,EAAE,EAAE;AAC1B,SAAQ,SACJ,QACA,KAAK,WAAW,GAChB,KAAK,OAAO,GACZ,KAAK,KAAK,OACV,KAAK,OAAO,MAAM;;CAE1B,YAAY;EACR,MAAM,WAAW,KAAK,KAAK;AAC3B,SAAO,SAAS,QAAQ,IAAI,KAAK,KAAK,WAAW,MAAM,WAAW;;CAEtE,QAAQ;AACJ,MAAI,KAAK,KAAK,SACR,KAAK,KAAK,UAAU,OAAO,KAAK,KAAK,SAAS,IAAI,IAC/C,CAAC,KAAK,KAAK,UAAU,OAAO,KAAK,KAAK,KAAK,KAAK,IACrD,QAAO,MAAM,KAAK,KAAK;MAGvB,QAAO;;CAGf,OAAO,OAAO;EACV,MAAM,eAAe,OAAO,MAAM;AAClC,SAAO,aAAa,SAAS,MAAM,eAAe;;;;;;ACxI1D,IAAa,UAAb,cAA6B,UAAU;CACnC,cAAc;AACV,QAAM,GAAG,UAAU;AACnB,OAAK,WAAW;;CAEpB,IAAI,OAAO;AACP,SAAO;;;;;;;;CAQX,SAAS;AACL,OAAK,OAAO;;;;;;;;CAQhB,MAAM,SAAS;AACX,OAAK,aAAa;EAClB,MAAM,cAAc;AAChB,QAAK,aAAa;AAClB,YAAS;;AAEb,MAAI,KAAK,YAAY,CAAC,KAAK,UAAU;GACjC,IAAI,QAAQ;AACZ,OAAI,KAAK,UAAU;AACf;AACA,SAAK,KAAK,gBAAgB,WAAY;AAClC,OAAE,SAAS,OAAO;MACpB;;AAEN,OAAI,CAAC,KAAK,UAAU;AAChB;AACA,SAAK,KAAK,SAAS,WAAY;AAC3B,OAAE,SAAS,OAAO;MACpB;;QAIN,QAAO;;;;;;;CAQf,QAAQ;AACJ,OAAK,WAAW;AAChB,OAAK,QAAQ;AACb,OAAK,aAAa,OAAO;;;;;;;CAO7B,OAAO,MAAM;EACT,MAAM,YAAY,WAAW;AAEzB,OAAI,cAAc,KAAK,cAAc,OAAO,SAAS,OACjD,MAAK,QAAQ;AAGjB,OAAI,YAAY,OAAO,MAAM;AACzB,SAAK,QAAQ,EAAE,aAAa,kCAAkC,CAAC;AAC/D,WAAO;;AAGX,QAAK,SAAS,OAAO;;AAGzB,gBAAc,MAAM,KAAK,OAAO,WAAW,CAAC,QAAQ,SAAS;AAE7D,MAAI,aAAa,KAAK,YAAY;AAE9B,QAAK,WAAW;AAChB,QAAK,aAAa,eAAe;AACjC,OAAI,WAAW,KAAK,WAChB,MAAK,OAAO;;;;;;;;CAWxB,UAAU;EACN,MAAM,cAAc;AAChB,QAAK,MAAM,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC;;AAEnC,MAAI,WAAW,KAAK,WAChB,QAAO;MAKP,MAAK,KAAK,QAAQ,MAAM;;;;;;;;CAShC,MAAM,SAAS;AACX,OAAK,WAAW;AAChB,gBAAc,UAAU,SAAS;AAC7B,QAAK,QAAQ,YAAY;AACrB,SAAK,WAAW;AAChB,SAAK,aAAa,QAAQ;KAC5B;IACJ;;;;;;;CAON,MAAM;EACF,MAAM,SAAS,KAAK,KAAK,SAAS,UAAU;EAC5C,MAAM,QAAQ,KAAK,SAAS,EAAE;AAE9B,MAAI,UAAU,KAAK,KAAK,kBACpB,OAAM,KAAK,KAAK,kBAAkB,cAAc;AAEpD,MAAI,CAAC,KAAK,kBAAkB,CAAC,MAAM,IAC/B,OAAM,MAAM;AAEhB,SAAO,KAAK,UAAU,QAAQ,MAAM;;;;;;AC7I5C,IAAI,QAAQ;AACZ,IAAI;AACA,SAAQ,OAAO,mBAAmB,eAC9B,qBAAqB,IAAI,gBAAgB;SAE1C,KAAK;AAIZ,MAAa,UAAU;;;;ACLvB,SAAS,QAAQ;AACjB,IAAa,UAAb,cAA6B,QAAQ;;;;;;;CAOjC,YAAY,MAAM;AACd,QAAM,KAAK;AACX,MAAI,OAAO,aAAa,aAAa;GACjC,MAAM,QAAQ,aAAa,SAAS;GACpC,IAAI,OAAO,SAAS;AAEpB,OAAI,CAAC,KACD,QAAO,QAAQ,QAAQ;AAE3B,QAAK,KACA,OAAO,aAAa,eACjB,KAAK,aAAa,SAAS,YAC3B,SAAS,KAAK;;;;;;;;;;CAU9B,QAAQ,MAAM,IAAI;EACd,MAAM,MAAM,KAAK,QAAQ;GACrB,QAAQ;GACF;GACT,CAAC;AACF,MAAI,GAAG,WAAW,GAAG;AACrB,MAAI,GAAG,UAAU,WAAW,YAAY;AACpC,QAAK,QAAQ,kBAAkB,WAAW,QAAQ;IACpD;;;;;;;CAON,SAAS;EACL,MAAM,MAAM,KAAK,SAAS;AAC1B,MAAI,GAAG,QAAQ,KAAK,OAAO,KAAK,KAAK,CAAC;AACtC,MAAI,GAAG,UAAU,WAAW,YAAY;AACpC,QAAK,QAAQ,kBAAkB,WAAW,QAAQ;IACpD;AACF,OAAK,UAAU;;;AAGvB,IAAa,UAAb,MAAa,gBAAgB,QAAQ;;;;;;;CAOjC,YAAY,eAAe,KAAK,MAAM;AAClC,SAAO;AACP,OAAK,gBAAgB;AACrB,wBAAsB,MAAM,KAAK;AACjC,OAAK,QAAQ;AACb,OAAK,UAAU,KAAK,UAAU;AAC9B,OAAK,OAAO;AACZ,OAAK,QAAQ,WAAc,KAAK,OAAO,KAAK,OAAO;AACnD,OAAK,SAAS;;;;;;;CAOlB,UAAU;EACN,IAAI;EACJ,MAAM,OAAO,KAAK,KAAK,OAAO,SAAS,OAAO,OAAO,cAAc,QAAQ,MAAM,WAAW,sBAAsB,YAAY;AAC9H,OAAK,UAAU,CAAC,CAAC,KAAK,MAAM;EAC5B,MAAM,MAAO,KAAK,OAAO,KAAK,cAAc,KAAK;AACjD,MAAI;AACA,OAAI,KAAK,KAAK,SAAS,KAAK,MAAM,KAAK;AACvC,OAAI;AACA,QAAI,KAAK,MAAM,cAAc;AAEzB,SAAI,yBAAyB,IAAI,sBAAsB,KAAK;AAC5D,UAAK,IAAI,KAAK,KAAK,MAAM,aACrB,KAAI,KAAK,MAAM,aAAa,eAAe,EAAE,CACzC,KAAI,iBAAiB,GAAG,KAAK,MAAM,aAAa,GAAG;;YAK5D,GAAG;AACV,OAAI,WAAW,KAAK,QAChB,KAAI;AACA,QAAI,iBAAiB,gBAAgB,2BAA2B;YAE7D,GAAG;AAEd,OAAI;AACA,QAAI,iBAAiB,UAAU,MAAM;YAElC,GAAG;AACV,IAAC,KAAK,KAAK,MAAM,eAAe,QAAQ,OAAO,KAAK,KAAa,GAAG,WAAW,IAAI;AAEnF,OAAI,qBAAqB,IACrB,KAAI,kBAAkB,KAAK,MAAM;AAErC,OAAI,KAAK,MAAM,eACX,KAAI,UAAU,KAAK,MAAM;AAE7B,OAAI,2BAA2B;IAC3B,IAAIC;AACJ,QAAI,IAAI,eAAe,EACnB,EAAC,OAAK,KAAK,MAAM,eAAe,QAAQA,SAAO,KAAK,KAAaA,KAAG,aAEpE,IAAI,kBAAkB,aAAa,CAAC;AAExC,QAAI,MAAM,IAAI,WACV;AACJ,QAAI,QAAQ,IAAI,UAAU,SAAS,IAAI,OACnC,MAAK,SAAS;QAKd,MAAK,mBAAmB;AACpB,UAAK,SAAS,OAAO,IAAI,WAAW,WAAW,IAAI,SAAS,EAAE;OAC/D,EAAE;;AAGb,OAAI,KAAK,KAAK,MAAM;WAEjB,GAAG;AAIN,QAAK,mBAAmB;AACpB,SAAK,SAAS,EAAE;MACjB,EAAE;AACL;;AAEJ,MAAI,OAAO,aAAa,aAAa;AACjC,QAAK,SAAS,QAAQ;AACtB,WAAQ,SAAS,KAAK,UAAU;;;;;;;;CAQxC,SAAS,KAAK;AACV,OAAK,aAAa,SAAS,KAAK,KAAK,KAAK;AAC1C,OAAK,SAAS,KAAK;;;;;;;CAOvB,SAAS,WAAW;AAChB,MAAI,gBAAgB,OAAO,KAAK,QAAQ,SAAS,KAAK,KAClD;AAEJ,OAAK,KAAK,qBAAqB;AAC/B,MAAI,UACA,KAAI;AACA,QAAK,KAAK,OAAO;WAEd,GAAG;AAEd,MAAI,OAAO,aAAa,YACpB,QAAO,QAAQ,SAAS,KAAK;AAEjC,OAAK,OAAO;;;;;;;CAOhB,UAAU;EACN,MAAM,OAAO,KAAK,KAAK;AACvB,MAAI,SAAS,MAAM;AACf,QAAK,aAAa,QAAQ,KAAK;AAC/B,QAAK,aAAa,UAAU;AAC5B,QAAK,UAAU;;;;;;;;CAQvB,QAAQ;AACJ,OAAK,UAAU;;;AAGvB,QAAQ,gBAAgB;AACxB,QAAQ,WAAW,EAAE;;;;;;AAMrB,IAAI,OAAO,aAAa,aAEpB;KAAI,OAAO,gBAAgB,WAEvB,aAAY,YAAY,cAAc;UAEjC,OAAO,qBAAqB,YAAY;EAC7C,MAAM,mBAAmB,gBAAgBC,iBAAa,aAAa;AACnE,mBAAiB,kBAAkB,eAAe,MAAM;;;AAGhE,SAAS,gBAAgB;AACrB,MAAK,IAAI,KAAK,QAAQ,SAClB,KAAI,QAAQ,SAAS,eAAe,EAAE,CAClC,SAAQ,SAAS,GAAG,OAAO;;AAIvC,IAAM,WAAW,WAAY;CACzB,MAAM,MAAM,WAAW,EACnB,SAAS,OACZ,CAAC;AACF,QAAO,OAAO,IAAI,iBAAiB;IACnC;;;;;;;;AAQJ,IAAa,MAAb,cAAyB,QAAQ;CAC7B,YAAY,MAAM;AACd,QAAM,KAAK;EACX,MAAM,cAAc,QAAQ,KAAK;AACjC,OAAK,iBAAiB,WAAW,CAAC;;CAEtC,QAAQ,OAAO,EAAE,EAAE;AACf,SAAO,OAAO,MAAM,EAAE,IAAI,KAAK,IAAI,EAAE,KAAK,KAAK;AAC/C,SAAO,IAAI,QAAQ,YAAY,KAAK,KAAK,EAAE,KAAK;;;AAGxD,SAAS,WAAW,MAAM;CACtB,MAAM,UAAU,KAAK;AAErB,KAAI;AACA,MAAI,gBAAgB,OAAO,mBAAmB,CAAC,WAAW,SACtD,QAAO,IAAI,gBAAgB;UAG5B,GAAG;AACV,KAAI,CAAC,QACD,KAAI;AACA,SAAO,IAAIA,eAAW,CAAC,SAAS,CAAC,OAAO,SAAS,CAAC,KAAK,IAAI,EAAE,oBAAoB;UAE9E,GAAG;;;;;ACvQlB,IAAM,gBAAgB,OAAO,cAAc,eACvC,OAAO,UAAU,YAAY,YAC7B,UAAU,QAAQ,aAAa,KAAK;AACxC,IAAa,SAAb,cAA4B,UAAU;CAClC,IAAI,OAAO;AACP,SAAO;;CAEX,SAAS;EACL,MAAM,MAAM,KAAK,KAAK;EACtB,MAAM,YAAY,KAAK,KAAK;EAE5B,MAAM,OAAO,gBACP,EAAE,GACF,KAAK,KAAK,MAAM,SAAS,qBAAqB,OAAO,OAAO,cAAc,QAAQ,MAAM,WAAW,sBAAsB,gBAAgB,mBAAmB,UAAU,cAAc,UAAU,sBAAsB;AAC1N,MAAI,KAAK,KAAK,aACV,MAAK,UAAU,KAAK,KAAK;AAE7B,MAAI;AACA,QAAK,KAAK,KAAK,aAAa,KAAK,WAAW,KAAK;WAE9C,KAAK;AACR,UAAO,KAAK,aAAa,SAAS,IAAI;;AAE1C,OAAK,GAAG,aAAa,KAAK,OAAO;AACjC,OAAK,mBAAmB;;;;;;;CAO5B,oBAAoB;AAChB,OAAK,GAAG,eAAe;AACnB,OAAI,KAAK,KAAK,UACV,MAAK,GAAG,QAAQ,OAAO;AAE3B,QAAK,QAAQ;;AAEjB,OAAK,GAAG,WAAW,eAAe,KAAK,QAAQ;GAC3C,aAAa;GACb,SAAS;GACZ,CAAC;AACF,OAAK,GAAG,aAAa,OAAO,KAAK,OAAO,GAAG,KAAK;AAChD,OAAK,GAAG,WAAW,MAAM,KAAK,QAAQ,mBAAmB,EAAE;;CAE/D,MAAM,SAAS;AACX,OAAK,WAAW;AAGhB,OAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;GACrC,MAAM,SAAS,QAAQ;GACvB,MAAM,aAAa,MAAM,QAAQ,SAAS;AAC1C,gBAAa,QAAQ,KAAK,iBAAiB,SAAS;AAIhD,QAAI;AACA,UAAK,QAAQ,QAAQ,KAAK;aAEvB,GAAG;AAEV,QAAI,WAGA,gBAAe;AACX,UAAK,WAAW;AAChB,UAAK,aAAa,QAAQ;OAC3B,KAAK,aAAa;KAE3B;;;CAGV,UAAU;AACN,MAAI,OAAO,KAAK,OAAO,aAAa;AAChC,QAAK,GAAG,gBAAgB;AACxB,QAAK,GAAG,OAAO;AACf,QAAK,KAAK;;;;;;;;CAQlB,MAAM;EACF,MAAM,SAAS,KAAK,KAAK,SAAS,QAAQ;EAC1C,MAAM,QAAQ,KAAK,SAAS,EAAE;AAE9B,MAAI,KAAK,KAAK,kBACV,OAAM,KAAK,KAAK,kBAAkB,cAAc;AAGpD,MAAI,CAAC,KAAK,eACN,OAAM,MAAM;AAEhB,SAAO,KAAK,UAAU,QAAQ,MAAM;;;AAG5C,IAAM,gBAAgBC,eAAW,aAAaA,eAAW;;;;;;;;;;AAUzD,IAAa,KAAb,cAAwB,OAAO;CAC3B,aAAa,KAAK,WAAW,MAAM;AAC/B,SAAO,CAAC,gBACF,YACI,IAAI,cAAc,KAAK,UAAU,GACjC,IAAI,cAAc,IAAI,GAC1B,IAAI,cAAc,KAAK,WAAW,KAAK;;CAEjD,QAAQ,SAAS,MAAM;AACnB,OAAK,GAAG,KAAK,KAAK;;;;;;;;;;;;;;AC/G1B,IAAa,KAAb,cAAwB,UAAU;CAC9B,IAAI,OAAO;AACP,SAAO;;CAEX,SAAS;AACL,MAAI;AAEA,QAAK,aAAa,IAAI,aAAa,KAAK,UAAU,QAAQ,EAAE,KAAK,KAAK,iBAAiB,KAAK,MAAM;WAE/F,KAAK;AACR,UAAO,KAAK,aAAa,SAAS,IAAI;;AAE1C,OAAK,WAAW,OACX,WAAW;AACZ,QAAK,SAAS;IAChB,CACG,OAAO,QAAQ;AAChB,QAAK,QAAQ,sBAAsB,IAAI;IACzC;AAEF,OAAK,WAAW,MAAM,WAAW;AAC7B,QAAK,WAAW,2BAA2B,CAAC,MAAM,WAAW;IACzD,MAAM,gBAAgB,0BAA0B,OAAO,kBAAkB,KAAK,OAAO,WAAW;IAChG,MAAM,SAAS,OAAO,SAAS,YAAY,cAAc,CAAC,WAAW;IACrE,MAAM,gBAAgB,2BAA2B;AACjD,kBAAc,SAAS,OAAO,OAAO,SAAS;AAC9C,SAAK,UAAU,cAAc,SAAS,WAAW;IACjD,MAAM,aAAa;AACf,YACK,MAAM,CACN,MAAM,EAAE,MAAM,qBAAY;AAC3B,UAAI,KACA;AAEJ,WAAK,SAASC,QAAM;AACpB,YAAM;OACR,CACG,OAAO,QAAQ,GAClB;;AAEN,UAAM;IACN,MAAM,SAAS,EAAE,MAAM,QAAQ;AAC/B,QAAI,KAAK,MAAM,IACX,QAAO,OAAO,WAAW,KAAK,MAAM,IAAI;AAE5C,SAAK,QAAQ,MAAM,OAAO,CAAC,WAAW,KAAK,QAAQ,CAAC;KACtD;IACJ;;CAEN,MAAM,SAAS;AACX,OAAK,WAAW;AAChB,OAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;GACrC,MAAM,SAAS,QAAQ;GACvB,MAAM,aAAa,MAAM,QAAQ,SAAS;AAC1C,QAAK,QAAQ,MAAM,OAAO,CAAC,WAAW;AAClC,QAAI,WACA,gBAAe;AACX,UAAK,WAAW;AAChB,UAAK,aAAa,QAAQ;OAC3B,KAAK,aAAa;KAE3B;;;CAGV,UAAU;EACN,IAAI;AACJ,GAAC,KAAK,KAAK,gBAAgB,QAAQ,OAAO,KAAK,KAAa,GAAG,OAAO;;;;;;AC1E9E,MAAa,aAAa;CACtB,WAAW;CACX,cAAc;CACd,SAAS;CACZ;;;;;;;;;;;;;;;;;;;;;;ACYD,IAAM,KAAK;AACX,IAAM,QAAQ;CACV;CAAU;CAAY;CAAa;CAAY;CAAQ;CAAY;CAAQ;CAAQ;CAAY;CAAQ;CAAa;CAAQ;CAAS;CACxI;AACD,SAAgB,MAAM,KAAK;AACvB,KAAI,IAAI,SAAS,IACb,OAAM;CAEV,MAAM,MAAM,KAAK,IAAI,IAAI,QAAQ,IAAI,EAAE,IAAI,IAAI,QAAQ,IAAI;AAC3D,KAAI,KAAK,MAAM,KAAK,GAChB,OAAM,IAAI,UAAU,GAAG,EAAE,GAAG,IAAI,UAAU,GAAG,EAAE,CAAC,QAAQ,MAAM,IAAI,GAAG,IAAI,UAAU,GAAG,IAAI,OAAO;CAErG,IAAI,IAAI,GAAG,KAAK,OAAO,GAAG,EAAE,MAAM,EAAE,EAAE,IAAI;AAC1C,QAAO,IACH,KAAI,MAAM,MAAM,EAAE,MAAM;AAE5B,KAAI,KAAK,MAAM,KAAK,IAAI;AACpB,MAAI,SAAS;AACb,MAAI,OAAO,IAAI,KAAK,UAAU,GAAG,IAAI,KAAK,SAAS,EAAE,CAAC,QAAQ,MAAM,IAAI;AACxE,MAAI,YAAY,IAAI,UAAU,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,MAAM,IAAI;AAClF,MAAI,UAAU;;AAElB,KAAI,YAAY,UAAU,KAAK,IAAI,QAAQ;AAC3C,KAAI,WAAW,SAAS,KAAK,IAAI,SAAS;AAC1C,QAAO;;AAEX,SAAS,UAAU,KAAK,MAAM;CAC1B,MAAM,OAAO,YAAY,QAAQ,KAAK,QAAQ,MAAM,IAAI,CAAC,MAAM,IAAI;AACnE,KAAI,KAAK,MAAM,GAAG,EAAE,IAAI,OAAO,KAAK,WAAW,EAC3C,OAAM,OAAO,GAAG,EAAE;AAEtB,KAAI,KAAK,MAAM,GAAG,IAAI,IAClB,OAAM,OAAO,MAAM,SAAS,GAAG,EAAE;AAErC,QAAO;;AAEX,SAAS,SAAS,KAAK,OAAO;CAC1B,MAAM,OAAO,EAAE;AACf,OAAM,QAAQ,6BAA6B,SAAU,IAAI,IAAI,IAAI;AAC7D,MAAI,GACA,MAAK,MAAM;GAEjB;AACF,QAAO;;;;;ACvDX,IAAM,qBAAqB,OAAO,qBAAqB,cACnD,OAAO,wBAAwB;AACnC,IAAM,0BAA0B,EAAE;AAClC,IAAI,mBAGA,kBAAiB,iBAAiB;AAC9B,yBAAwB,SAAS,aAAa,UAAU,CAAC;GAC1D,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAyBb,IAAa,uBAAb,MAAa,6BAA6B,QAAQ;;;;;;;CAO9C,YAAY,KAAK,MAAM;AACnB,SAAO;AACP,OAAK,aAAa;AAClB,OAAK,cAAc,EAAE;AACrB,OAAK,iBAAiB;AACtB,OAAK,gBAAgB;AACrB,OAAK,eAAe;AACpB,OAAK,cAAc;;;;;AAKnB,OAAK,mBAAmB;AACxB,MAAI,OAAO,aAAa,OAAO,KAAK;AAChC,UAAO;AACP,SAAM;;AAEV,MAAI,KAAK;GACL,MAAM,YAAY,MAAM,IAAI;AAC5B,QAAK,WAAW,UAAU;AAC1B,QAAK,SACD,UAAU,aAAa,WAAW,UAAU,aAAa;AAC7D,QAAK,OAAO,UAAU;AACtB,OAAI,UAAU,MACV,MAAK,QAAQ,UAAU;aAEtB,KAAK,KACV,MAAK,WAAW,MAAM,KAAK,KAAK,CAAC;AAErC,wBAAsB,MAAM,KAAK;AACjC,OAAK,SACD,QAAQ,KAAK,SACP,KAAK,SACL,OAAO,aAAa,eAAe,aAAa,SAAS;AACnE,MAAI,KAAK,YAAY,CAAC,KAAK,KAEvB,MAAK,OAAO,KAAK,SAAS,QAAQ;AAEtC,OAAK,WACD,KAAK,aACA,OAAO,aAAa,cAAc,SAAS,WAAW;AAC/D,OAAK,OACD,KAAK,SACA,OAAO,aAAa,eAAe,SAAS,OACvC,SAAS,OACT,KAAK,SACD,QACA;AAClB,OAAK,aAAa,EAAE;AACpB,OAAK,oBAAoB,EAAE;AAC3B,OAAK,WAAW,SAAS,MAAM;GAC3B,MAAM,gBAAgB,EAAE,UAAU;AAClC,QAAK,WAAW,KAAK,cAAc;AACnC,QAAK,kBAAkB,iBAAiB;IAC1C;AACF,OAAK,OAAO,OAAO,OAAO;GACtB,MAAM;GACN,OAAO;GACP,iBAAiB;GACjB,SAAS;GACT,gBAAgB;GAChB,iBAAiB;GACjB,kBAAkB;GAClB,oBAAoB;GACpB,mBAAmB,EACf,WAAW,MACd;GACD,kBAAkB,EAAE;GACpB,qBAAqB;GACxB,EAAE,KAAK;AACR,OAAK,KAAK,OACN,KAAK,KAAK,KAAK,QAAQ,OAAO,GAAG,IAC5B,KAAK,KAAK,mBAAmB,MAAM;AAC5C,MAAI,OAAO,KAAK,KAAK,UAAU,SAC3B,MAAK,KAAK,QAAQ,OAAO,KAAK,KAAK,MAAM;AAE7C,MAAI,oBAAoB;AACpB,OAAI,KAAK,KAAK,qBAAqB;AAI/B,SAAK,mCAAmC;AACpC,SAAI,KAAK,WAAW;AAEhB,WAAK,UAAU,oBAAoB;AACnC,WAAK,UAAU,OAAO;;;AAG9B,qBAAiB,gBAAgB,KAAK,4BAA4B,MAAM;;AAE5E,OAAI,KAAK,aAAa,aAAa;AAC/B,SAAK,8BAA8B;AAC/B,UAAK,SAAS,mBAAmB,EAC7B,aAAa,2BAChB,CAAC;;AAEN,4BAAwB,KAAK,KAAK,sBAAsB;;;AAGhE,MAAI,KAAK,KAAK,gBACV,MAAK,aAAa,iCAAiB;AAEvC,OAAK,OAAO;;;;;;;;;CAShB,gBAAgB,MAAM;EAClB,MAAM,QAAQ,OAAO,OAAO,EAAE,EAAE,KAAK,KAAK,MAAM;AAEhD,QAAM,MAAMC;AAEZ,QAAM,YAAY;AAElB,MAAI,KAAK,GACL,OAAM,MAAM,KAAK;EACrB,MAAM,OAAO,OAAO,OAAO,EAAE,EAAE,KAAK,MAAM;GACtC;GACA,QAAQ;GACR,UAAU,KAAK;GACf,QAAQ,KAAK;GACb,MAAM,KAAK;GACd,EAAE,KAAK,KAAK,iBAAiB,MAAM;AACpC,SAAO,IAAI,KAAK,kBAAkB,MAAM,KAAK;;;;;;;CAOjD,QAAQ;AACJ,MAAI,KAAK,WAAW,WAAW,GAAG;AAE9B,QAAK,mBAAmB;AACpB,SAAK,aAAa,SAAS,0BAA0B;MACtD,EAAE;AACL;;EAEJ,MAAM,gBAAgB,KAAK,KAAK,mBAC5B,qBAAqB,yBACrB,KAAK,WAAW,QAAQ,YAAY,KAAK,KACvC,cACA,KAAK,WAAW;AACtB,OAAK,aAAa;EAClB,MAAM,YAAY,KAAK,gBAAgB,cAAc;AACrD,YAAU,MAAM;AAChB,OAAK,aAAa,UAAU;;;;;;;CAOhC,aAAa,WAAW;AACpB,MAAI,KAAK,UACL,MAAK,UAAU,oBAAoB;AAGvC,OAAK,YAAY;AAEjB,YACK,GAAG,SAAS,KAAK,SAAS,KAAK,KAAK,CAAC,CACrC,GAAG,UAAU,KAAK,UAAU,KAAK,KAAK,CAAC,CACvC,GAAG,SAAS,KAAK,SAAS,KAAK,KAAK,CAAC,CACrC,GAAG,UAAU,WAAW,KAAK,SAAS,mBAAmB,OAAO,CAAC;;;;;;;CAO1E,SAAS;AACL,OAAK,aAAa;AAClB,uBAAqB,wBACjB,gBAAgB,KAAK,UAAU;AACnC,OAAK,aAAa,OAAO;AACzB,OAAK,OAAO;;;;;;;CAOhB,UAAU,QAAQ;AACd,MAAI,cAAc,KAAK,cACnB,WAAW,KAAK,cAChB,cAAc,KAAK,YAAY;AAC/B,QAAK,aAAa,UAAU,OAAO;AAEnC,QAAK,aAAa,YAAY;AAC9B,WAAQ,OAAO,MAAf;IACI,KAAK;AACD,UAAK,YAAY,KAAK,MAAM,OAAO,KAAK,CAAC;AACzC;IACJ,KAAK;AACD,UAAK,YAAY,OAAO;AACxB,UAAK,aAAa,OAAO;AACzB,UAAK,aAAa,OAAO;AACzB,UAAK,mBAAmB;AACxB;IACJ,KAAK;KACD,MAAM,sBAAM,IAAI,MAAM,eAAe;AAErC,SAAI,OAAO,OAAO;AAClB,UAAK,SAAS,IAAI;AAClB;IACJ,KAAK;AACD,UAAK,aAAa,QAAQ,OAAO,KAAK;AACtC,UAAK,aAAa,WAAW,OAAO,KAAK;AACzC;;;;;;;;;;CAYhB,YAAY,MAAM;AACd,OAAK,aAAa,aAAa,KAAK;AACpC,OAAK,KAAK,KAAK;AACf,OAAK,UAAU,MAAM,MAAM,KAAK;AAChC,OAAK,gBAAgB,KAAK;AAC1B,OAAK,eAAe,KAAK;AACzB,OAAK,cAAc,KAAK;AACxB,OAAK,QAAQ;AAEb,MAAI,aAAa,KAAK,WAClB;AACJ,OAAK,mBAAmB;;;;;;;CAO5B,oBAAoB;AAChB,OAAK,eAAe,KAAK,kBAAkB;EAC3C,MAAM,QAAQ,KAAK,gBAAgB,KAAK;AACxC,OAAK,mBAAmB,KAAK,KAAK,GAAG;AACrC,OAAK,oBAAoB,KAAK,mBAAmB;AAC7C,QAAK,SAAS,eAAe;KAC9B,MAAM;AACT,MAAI,KAAK,KAAK,UACV,MAAK,kBAAkB,OAAO;;;;;;;CAQtC,WAAW;AACP,OAAK,YAAY,OAAO,GAAG,KAAK,eAAe;AAI/C,OAAK,iBAAiB;AACtB,MAAI,MAAM,KAAK,YAAY,OACvB,MAAK,aAAa,QAAQ;MAG1B,MAAK,OAAO;;;;;;;CAQpB,QAAQ;AACJ,MAAI,aAAa,KAAK,cAClB,KAAK,UAAU,YACf,CAAC,KAAK,aACN,KAAK,YAAY,QAAQ;GACzB,MAAM,UAAU,KAAK,qBAAqB;AAC1C,QAAK,UAAU,KAAK,QAAQ;AAG5B,QAAK,iBAAiB,QAAQ;AAC9B,QAAK,aAAa,QAAQ;;;;;;;;;CASlC,sBAAsB;AAIlB,MAAI,EAH2B,KAAK,eAChC,KAAK,UAAU,SAAS,aACxB,KAAK,YAAY,SAAS,GAE1B,QAAO,KAAK;EAEhB,IAAI,cAAc;AAClB,OAAK,IAAI,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;GAC9C,MAAM,OAAO,KAAK,YAAY,GAAG;AACjC,OAAI,KACA,gBAAe,WAAW,KAAK;AAEnC,OAAI,IAAI,KAAK,cAAc,KAAK,YAC5B,QAAO,KAAK,YAAY,MAAM,GAAG,EAAE;AAEvC,kBAAe;;AAEnB,SAAO,KAAK;;;;;;;;;;;CAWF,kBAAkB;AAC5B,MAAI,CAAC,KAAK,iBACN,QAAO;EACX,MAAM,aAAa,KAAK,KAAK,GAAG,KAAK;AACrC,MAAI,YAAY;AACZ,QAAK,mBAAmB;AACxB,kBAAe;AACX,SAAK,SAAS,eAAe;MAC9B,KAAK,aAAa;;AAEzB,SAAO;;;;;;;;;;CAUX,MAAM,KAAK,SAAS,IAAI;AACpB,OAAK,YAAY,WAAW,KAAK,SAAS,GAAG;AAC7C,SAAO;;;;;;;;;;CAUX,KAAK,KAAK,SAAS,IAAI;AACnB,OAAK,YAAY,WAAW,KAAK,SAAS,GAAG;AAC7C,SAAO;;;;;;;;;;;CAWX,YAAY,MAAM,MAAM,SAAS,IAAI;AACjC,MAAI,eAAe,OAAO,MAAM;AAC5B,QAAK;AACL,UAAO;;AAEX,MAAI,eAAe,OAAO,SAAS;AAC/B,QAAK;AACL,aAAU;;AAEd,MAAI,cAAc,KAAK,cAAc,aAAa,KAAK,WACnD;AAEJ,YAAU,WAAW,EAAE;AACvB,UAAQ,WAAW,UAAU,QAAQ;EACrC,MAAM,SAAS;GACL;GACA;GACG;GACZ;AACD,OAAK,aAAa,gBAAgB,OAAO;AACzC,OAAK,YAAY,KAAK,OAAO;AAC7B,MAAI,GACA,MAAK,KAAK,SAAS,GAAG;AAC1B,OAAK,OAAO;;;;;CAKhB,QAAQ;EACJ,MAAM,cAAc;AAChB,QAAK,SAAS,eAAe;AAC7B,QAAK,UAAU,OAAO;;EAE1B,MAAM,wBAAwB;AAC1B,QAAK,IAAI,WAAW,gBAAgB;AACpC,QAAK,IAAI,gBAAgB,gBAAgB;AACzC,UAAO;;EAEX,MAAM,uBAAuB;AAEzB,QAAK,KAAK,WAAW,gBAAgB;AACrC,QAAK,KAAK,gBAAgB,gBAAgB;;AAE9C,MAAI,cAAc,KAAK,cAAc,WAAW,KAAK,YAAY;AAC7D,QAAK,aAAa;AAClB,OAAI,KAAK,YAAY,OACjB,MAAK,KAAK,eAAe;AACrB,QAAI,KAAK,UACL,iBAAgB;QAGhB,QAAO;KAEb;YAEG,KAAK,UACV,iBAAgB;OAGhB,QAAO;;AAGf,SAAO;;;;;;;CAOX,SAAS,KAAK;AACV,uBAAqB,wBAAwB;AAC7C,MAAI,KAAK,KAAK,oBACV,KAAK,WAAW,SAAS,KACzB,KAAK,eAAe,WAAW;AAC/B,QAAK,WAAW,OAAO;AACvB,UAAO,KAAK,OAAO;;AAEvB,OAAK,aAAa,SAAS,IAAI;AAC/B,OAAK,SAAS,mBAAmB,IAAI;;;;;;;CAOzC,SAAS,QAAQ,aAAa;AAC1B,MAAI,cAAc,KAAK,cACnB,WAAW,KAAK,cAChB,cAAc,KAAK,YAAY;AAE/B,QAAK,eAAe,KAAK,kBAAkB;AAE3C,QAAK,UAAU,mBAAmB,QAAQ;AAE1C,QAAK,UAAU,OAAO;AAEtB,QAAK,UAAU,oBAAoB;AACnC,OAAI,oBAAoB;AACpB,QAAI,KAAK,2BACL,qBAAoB,gBAAgB,KAAK,4BAA4B,MAAM;AAE/E,QAAI,KAAK,uBAAuB;KAC5B,MAAM,IAAI,wBAAwB,QAAQ,KAAK,sBAAsB;AACrE,SAAI,MAAM,GACN,yBAAwB,OAAO,GAAG,EAAE;;;AAKhD,QAAK,aAAa;AAElB,QAAK,KAAK;AAEV,QAAK,aAAa,SAAS,QAAQ,YAAY;AAG/C,QAAK,cAAc,EAAE;AACrB,QAAK,iBAAiB;;;;AAIlC,qBAAqB,WAAWA;;;;;;;;;;;;;;;;;;;;;;;;AAwBhC,IAAa,oBAAb,cAAuC,qBAAqB;CACxD,cAAc;AACV,QAAM,GAAG,UAAU;AACnB,OAAK,YAAY,EAAE;;CAEvB,SAAS;AACL,QAAM,QAAQ;AACd,MAAI,WAAW,KAAK,cAAc,KAAK,KAAK,QACxC,MAAK,IAAI,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,IACvC,MAAK,OAAO,KAAK,UAAU,GAAG;;;;;;;;CAU1C,OAAO,MAAM;EACT,IAAI,YAAY,KAAK,gBAAgB,KAAK;EAC1C,IAAI,SAAS;AACb,uBAAqB,wBAAwB;EAC7C,MAAM,wBAAwB;AAC1B,OAAI,OACA;AACJ,aAAU,KAAK,CAAC;IAAE,MAAM;IAAQ,MAAM;IAAS,CAAC,CAAC;AACjD,aAAU,KAAK,WAAW,QAAQ;AAC9B,QAAI,OACA;AACJ,QAAI,WAAW,IAAI,QAAQ,YAAY,IAAI,MAAM;AAC7C,UAAK,YAAY;AACjB,UAAK,aAAa,aAAa,UAAU;AACzC,SAAI,CAAC,UACD;AACJ,0BAAqB,wBACjB,gBAAgB,UAAU;AAC9B,UAAK,UAAU,YAAY;AACvB,UAAI,OACA;AACJ,UAAI,aAAa,KAAK,WAClB;AACJ,eAAS;AACT,WAAK,aAAa,UAAU;AAC5B,gBAAU,KAAK,CAAC,EAAE,MAAM,WAAW,CAAC,CAAC;AACrC,WAAK,aAAa,WAAW,UAAU;AACvC,kBAAY;AACZ,WAAK,YAAY;AACjB,WAAK,OAAO;OACd;WAED;KACD,MAAM,sBAAM,IAAI,MAAM,cAAc;AAEpC,SAAI,YAAY,UAAU;AAC1B,UAAK,aAAa,gBAAgB,IAAI;;KAE5C;;EAEN,SAAS,kBAAkB;AACvB,OAAI,OACA;AAEJ,YAAS;AACT,YAAS;AACT,aAAU,OAAO;AACjB,eAAY;;EAGhB,MAAM,WAAW,QAAQ;GACrB,MAAM,wBAAQ,IAAI,MAAM,kBAAkB,IAAI;AAE9C,SAAM,YAAY,UAAU;AAC5B,oBAAiB;AACjB,QAAK,aAAa,gBAAgB,MAAM;;EAE5C,SAAS,mBAAmB;AACxB,WAAQ,mBAAmB;;EAG/B,SAAS,UAAU;AACf,WAAQ,gBAAgB;;EAG5B,SAAS,UAAU,IAAI;AACnB,OAAI,aAAa,GAAG,SAAS,UAAU,KACnC,kBAAiB;;EAIzB,MAAM,gBAAgB;AAClB,aAAU,eAAe,QAAQ,gBAAgB;AACjD,aAAU,eAAe,SAAS,QAAQ;AAC1C,aAAU,eAAe,SAAS,iBAAiB;AACnD,QAAK,IAAI,SAAS,QAAQ;AAC1B,QAAK,IAAI,aAAa,UAAU;;AAEpC,YAAU,KAAK,QAAQ,gBAAgB;AACvC,YAAU,KAAK,SAAS,QAAQ;AAChC,YAAU,KAAK,SAAS,iBAAiB;AACzC,OAAK,KAAK,SAAS,QAAQ;AAC3B,OAAK,KAAK,aAAa,UAAU;AACjC,MAAI,KAAK,UAAU,QAAQ,eAAe,KAAK,MAC3C,SAAS,eAET,MAAK,mBAAmB;AACpB,OAAI,CAAC,OACD,WAAU,MAAM;KAErB,IAAI;MAGP,WAAU,MAAM;;CAGxB,YAAY,MAAM;AACd,OAAK,YAAY,KAAK,gBAAgB,KAAK,SAAS;AACpD,QAAM,YAAY,KAAK;;;;;;;;CAQ3B,gBAAgB,UAAU;EACtB,MAAM,mBAAmB,EAAE;AAC3B,OAAK,IAAI,IAAI,GAAG,IAAI,SAAS,QAAQ,IACjC,KAAI,CAAC,KAAK,WAAW,QAAQ,SAAS,GAAG,CACrC,kBAAiB,KAAK,SAAS,GAAG;AAE1C,SAAO;;;;;;;;;;;;;;;;;;;;;;AAsBf,IAAaC,WAAb,cAA4B,kBAAkB;CAC1C,YAAY,KAAK,OAAO,EAAE,EAAE;EACxB,MAAM,IAAI,OAAO,QAAQ,WAAW,MAAM;AAC1C,MAAI,CAAC,EAAE,cACF,EAAE,cAAc,OAAO,EAAE,WAAW,OAAO,SAC5C,GAAE,cAAc,EAAE,cAAc;GAAC;GAAW;GAAa;GAAe,EACnE,KAAK,kBAAkBC,WAAmB,eAAe,CACzD,QAAQ,MAAM,CAAC,CAAC,EAAE;AAE3B,QAAM,KAAK,EAAE;;;;;;;;;;;;;;;AC1sBrB,IAAa,QAAb,cAA2B,QAAQ;CAC/B,SAAS;AACL,OAAK,QAAQ,CACR,MAAM,QAAQ;AACf,OAAI,CAAC,IAAI,GACL,QAAO,KAAK,QAAQ,oBAAoB,IAAI,QAAQ,IAAI;AAE5D,OAAI,MAAM,CAAC,MAAM,SAAS,KAAK,OAAO,KAAK,CAAC;IAC9C,CACG,OAAO,QAAQ;AAChB,QAAK,QAAQ,oBAAoB,IAAI;IACvC;;CAEN,QAAQ,MAAM,UAAU;AACpB,OAAK,OAAO,KAAK,CACZ,MAAM,QAAQ;AACf,OAAI,CAAC,IAAI,GACL,QAAO,KAAK,QAAQ,qBAAqB,IAAI,QAAQ,IAAI;AAE7D,aAAU;IACZ,CACG,OAAO,QAAQ;AAChB,QAAK,QAAQ,qBAAqB,IAAI;IACxC;;CAEN,OAAO,MAAM;EACT,IAAI;EACJ,MAAM,SAAS,SAAS;EACxB,MAAM,UAAU,IAAI,QAAQ,KAAK,KAAK,aAAa;AACnD,MAAI,OACA,SAAQ,IAAI,gBAAgB,2BAA2B;AAE3D,GAAC,KAAK,KAAK,OAAO,gBAAgB,QAAQ,OAAO,KAAK,KAAa,GAAG,cAAc,QAAQ;AAC5F,SAAO,MAAM,KAAK,KAAK,EAAE;GACrB,QAAQ,SAAS,SAAS;GAC1B,MAAM,SAAS,OAAO;GACtB;GACA,aAAa,KAAK,KAAK,kBAAkB,YAAY;GACxD,CAAC,CAAC,MAAM,QAAQ;GACb,IAAIC;AAEJ,IAAC,OAAK,KAAK,OAAO,gBAAgB,QAAQA,SAAO,KAAK,KAAaA,KAAG,aAAa,IAAI,QAAQ,cAAc,CAAC;AAC9G,UAAO;IACT;;;;;;AClDV,MAAaC,aAAWC,SAAO;;;;;;;;;;;;;ACO/B,SAAgB,IAAI,KAAK,OAAO,IAAI,KAAK;CACrC,IAAI,MAAM;AAEV,OAAM,OAAQ,OAAO,aAAa,eAAe;AACjD,KAAI,QAAQ,IACR,OAAM,IAAI,WAAW,OAAO,IAAI;AAEpC,KAAI,OAAO,QAAQ,UAAU;AACzB,MAAI,QAAQ,IAAI,OAAO,EAAE,CACrB,KAAI,QAAQ,IAAI,OAAO,EAAE,CACrB,OAAM,IAAI,WAAW;MAGrB,OAAM,IAAI,OAAO;AAGzB,MAAI,CAAC,sBAAsB,KAAK,IAAI,CAChC,KAAI,gBAAgB,OAAO,IACvB,OAAM,IAAI,WAAW,OAAO;MAG5B,OAAM,aAAa;AAI3B,QAAM,MAAM,IAAI;;AAGpB,KAAI,CAAC,IAAI,MACL;MAAI,cAAc,KAAK,IAAI,SAAS,CAChC,KAAI,OAAO;WAEN,eAAe,KAAK,IAAI,SAAS,CACtC,KAAI,OAAO;;AAGnB,KAAI,OAAO,IAAI,QAAQ;CAEvB,MAAM,OADO,IAAI,KAAK,QAAQ,IAAI,KAAK,KACnB,MAAM,IAAI,OAAO,MAAM,IAAI;AAE/C,KAAI,KAAK,IAAI,WAAW,QAAQ,OAAO,MAAM,IAAI,OAAO;AAExD,KAAI,OACA,IAAI,WACA,QACA,QACC,OAAO,IAAI,SAAS,IAAI,OAAO,KAAK,MAAM,IAAI;AACvD,QAAO;;;;;ACzDX,IAAM,wBAAwB,OAAO,gBAAgB;AACrD,IAAM,UAAU,QAAQ;AACpB,QAAO,OAAO,YAAY,WAAW,aAC/B,YAAY,OAAO,IAAI,GACvB,IAAI,kBAAkB;;AAEhC,IAAM,WAAW,OAAO,UAAU;AAClC,IAAM,iBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,SAAS,KAAK,KAAK,KAAK;AAChC,IAAM,iBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,SAAS,KAAK,KAAK,KAAK;;;;;;AAMhC,SAAgB,SAAS,KAAK;AAC1B,QAAS,0BAA0B,eAAe,eAAe,OAAO,IAAI,KACvE,kBAAkB,eAAe,QACjC,kBAAkB,eAAe;;AAE1C,SAAgB,UAAU,KAAK,QAAQ;AACnC,KAAI,CAAC,OAAO,OAAO,QAAQ,SACvB,QAAO;AAEX,KAAI,MAAM,QAAQ,IAAI,EAAE;AACpB,OAAK,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,IACnC,KAAI,UAAU,IAAI,GAAG,CACjB,QAAO;AAGf,SAAO;;AAEX,KAAI,SAAS,IAAI,CACb,QAAO;AAEX,KAAI,IAAI,UACJ,OAAO,IAAI,WAAW,cACtB,UAAU,WAAW,EACrB,QAAO,UAAU,IAAI,QAAQ,EAAE,KAAK;AAExC,MAAK,MAAM,OAAO,IACd,KAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,IAAI,UAAU,IAAI,KAAK,CACrE,QAAO;AAGf,QAAO;;;;;;;;;;;;ACxCX,SAAgB,kBAAkB,QAAQ;CACtC,MAAM,UAAU,EAAE;CAClB,MAAM,aAAa,OAAO;CAC1B,MAAM,OAAO;AACb,MAAK,OAAO,mBAAmB,YAAY,QAAQ;AACnD,MAAK,cAAc,QAAQ;AAC3B,QAAO;EAAE,QAAQ;EAAe;EAAS;;AAE7C,SAAS,mBAAmB,MAAM,SAAS;AACvC,KAAI,CAAC,KACD,QAAO;AACX,KAAI,SAAS,KAAK,EAAE;EAChB,MAAM,cAAc;GAAE,cAAc;GAAM,KAAK,QAAQ;GAAQ;AAC/D,UAAQ,KAAK,KAAK;AAClB,SAAO;YAEF,MAAM,QAAQ,KAAK,EAAE;EAC1B,MAAM,UAAU,IAAI,MAAM,KAAK,OAAO;AACtC,OAAK,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,IAC7B,SAAQ,KAAK,mBAAmB,KAAK,IAAI,QAAQ;AAErD,SAAO;YAEF,OAAO,SAAS,YAAY,EAAE,gBAAgB,OAAO;EAC1D,MAAM,UAAU,EAAE;AAClB,OAAK,MAAM,OAAO,KACd,KAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,CAC/C,SAAQ,OAAO,mBAAmB,KAAK,MAAM,QAAQ;AAG7D,SAAO;;AAEX,QAAO;;;;;;;;;;AAUX,SAAgB,kBAAkB,QAAQ,SAAS;AAC/C,QAAO,OAAO,mBAAmB,OAAO,MAAM,QAAQ;AACtD,QAAO,OAAO;AACd,QAAO;;AAEX,SAAS,mBAAmB,MAAM,SAAS;AACvC,KAAI,CAAC,KACD,QAAO;AACX,KAAI,QAAQ,KAAK,iBAAiB,KAI9B,KAHqB,OAAO,KAAK,QAAQ,YACrC,KAAK,OAAO,KACZ,KAAK,MAAM,QAAQ,OAEnB,QAAO,QAAQ,KAAK;KAGpB,OAAM,IAAI,MAAM,sBAAsB;UAGrC,MAAM,QAAQ,KAAK,CACxB,MAAK,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,IAC7B,MAAK,KAAK,mBAAmB,KAAK,IAAI,QAAQ;UAG7C,OAAO,SAAS,UACrB;OAAK,MAAM,OAAO,KACd,KAAI,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,CAC/C,MAAK,OAAO,mBAAmB,KAAK,MAAM,QAAQ;;AAI9D,QAAO;;;;;;;;;;;;;;AC3EX,IAAMC,oBAAkB;CACpB;CACA;CACA;CACA;CACA;CACA;CACH;;;;;;AAMD,MAAa,WAAW;AACxB,IAAW;CACV,SAAU,cAAY;AACnB,cAAW,aAAW,aAAa,KAAK;AACxC,cAAW,aAAW,gBAAgB,KAAK;AAC3C,cAAW,aAAW,WAAW,KAAK;AACtC,cAAW,aAAW,SAAS,KAAK;AACpC,cAAW,aAAW,mBAAmB,KAAK;AAC9C,cAAW,aAAW,kBAAkB,KAAK;AAC7C,cAAW,aAAW,gBAAgB,KAAK;GAC5C,eAAe,aAAa,EAAE,EAAE;;;;AAInC,IAAa,UAAb,MAAqB;;;;;;CAMjB,YAAY,UAAU;AAClB,OAAK,WAAW;;;;;;;;CAQpB,OAAO,KAAK;AACR,MAAI,IAAI,SAAS,WAAW,SAAS,IAAI,SAAS,WAAW,KACzD;OAAI,UAAU,IAAI,CACd,QAAO,KAAK,eAAe;IACvB,MAAM,IAAI,SAAS,WAAW,QACxB,WAAW,eACX,WAAW;IACjB,KAAK,IAAI;IACT,MAAM,IAAI;IACV,IAAI,IAAI;IACX,CAAC;;AAGV,SAAO,CAAC,KAAK,eAAe,IAAI,CAAC;;;;;CAKrC,eAAe,KAAK;EAEhB,IAAI,MAAM,KAAK,IAAI;AAEnB,MAAI,IAAI,SAAS,WAAW,gBACxB,IAAI,SAAS,WAAW,WACxB,QAAO,IAAI,cAAc;AAI7B,MAAI,IAAI,OAAO,QAAQ,IAAI,IACvB,QAAO,IAAI,MAAM;AAGrB,MAAI,QAAQ,IAAI,GACZ,QAAO,IAAI;AAGf,MAAI,QAAQ,IAAI,KACZ,QAAO,KAAK,UAAU,IAAI,MAAM,KAAK,SAAS;AAElD,SAAO;;;;;;;CAOX,eAAe,KAAK;EAChB,MAAM,iBAAiB,kBAAkB,IAAI;EAC7C,MAAM,OAAO,KAAK,eAAe,eAAe,OAAO;EACvD,MAAM,UAAU,eAAe;AAC/B,UAAQ,QAAQ,KAAK;AACrB,SAAO;;;AAIf,SAAS,SAAS,SAAO;AACrB,QAAO,OAAO,UAAU,SAAS,KAAKC,QAAM,KAAK;;;;;;;AAOrD,IAAa,UAAb,MAAa,gBAAgB,QAAQ;;;;;;CAMjC,YAAY,SAAS;AACjB,SAAO;AACP,OAAK,UAAU;;;;;;;CAOnB,IAAI,KAAK;EACL,IAAI;AACJ,MAAI,OAAO,QAAQ,UAAU;AACzB,OAAI,KAAK,cACL,OAAM,IAAI,MAAM,kDAAkD;AAEtE,YAAS,KAAK,aAAa,IAAI;GAC/B,MAAM,gBAAgB,OAAO,SAAS,WAAW;AACjD,OAAI,iBAAiB,OAAO,SAAS,WAAW,YAAY;AACxD,WAAO,OAAO,gBAAgB,WAAW,QAAQ,WAAW;AAE5D,SAAK,gBAAgB,IAAI,oBAAoB,OAAO;AAEpD,QAAI,OAAO,gBAAgB,EACvB,OAAM,aAAa,WAAW,OAAO;SAKzC,OAAM,aAAa,WAAW,OAAO;aAGpC,SAAS,IAAI,IAAI,IAAI,OAE1B,KAAI,CAAC,KAAK,cACN,OAAM,IAAI,MAAM,mDAAmD;OAElE;AACD,YAAS,KAAK,cAAc,eAAe,IAAI;AAC/C,OAAI,QAAQ;AAER,SAAK,gBAAgB;AACrB,UAAM,aAAa,WAAW,OAAO;;;MAK7C,OAAM,IAAI,MAAM,mBAAmB,IAAI;;;;;;;;CAS/C,aAAa,KAAK;EACd,IAAI,IAAI;EAER,MAAM,IAAI,EACN,MAAM,OAAO,IAAI,OAAO,EAAE,CAAC,EAC9B;AACD,MAAI,WAAW,EAAE,UAAU,OACvB,OAAM,IAAI,MAAM,yBAAyB,EAAE,KAAK;AAGpD,MAAI,EAAE,SAAS,WAAW,gBACtB,EAAE,SAAS,WAAW,YAAY;GAClC,MAAM,QAAQ,IAAI;AAClB,UAAO,IAAI,OAAO,EAAE,EAAE,KAAK,OAAO,KAAK,IAAI;GAC3C,MAAM,MAAM,IAAI,UAAU,OAAO,EAAE;AACnC,OAAI,OAAO,OAAO,IAAI,IAAI,IAAI,OAAO,EAAE,KAAK,IACxC,OAAM,IAAI,MAAM,sBAAsB;AAE1C,KAAE,cAAc,OAAO,IAAI;;AAG/B,MAAI,QAAQ,IAAI,OAAO,IAAI,EAAE,EAAE;GAC3B,MAAM,QAAQ,IAAI;AAClB,UAAO,EAAE,GAAG;AAER,QAAI,QADM,IAAI,OAAO,EAAE,CAEnB;AACJ,QAAI,MAAM,IAAI,OACV;;AAER,KAAE,MAAM,IAAI,UAAU,OAAO,EAAE;QAG/B,GAAE,MAAM;EAGZ,MAAM,OAAO,IAAI,OAAO,IAAI,EAAE;AAC9B,MAAI,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM;GACrC,MAAM,QAAQ,IAAI;AAClB,UAAO,EAAE,GAAG;IACR,MAAM,IAAI,IAAI,OAAO,EAAE;AACvB,QAAI,QAAQ,KAAK,OAAO,EAAE,IAAI,GAAG;AAC7B,OAAE;AACF;;AAEJ,QAAI,MAAM,IAAI,OACV;;AAER,KAAE,KAAK,OAAO,IAAI,UAAU,OAAO,IAAI,EAAE,CAAC;;AAG9C,MAAI,IAAI,OAAO,EAAE,EAAE,EAAE;GACjB,MAAM,UAAU,KAAK,SAAS,IAAI,OAAO,EAAE,CAAC;AAC5C,OAAI,QAAQ,eAAe,EAAE,MAAM,QAAQ,CACvC,GAAE,OAAO;OAGT,OAAM,IAAI,MAAM,kBAAkB;;AAG1C,SAAO;;CAEX,SAAS,KAAK;AACV,MAAI;AACA,UAAO,KAAK,MAAM,KAAK,KAAK,QAAQ;WAEjC,GAAG;AACN,UAAO;;;CAGf,OAAO,eAAe,MAAM,SAAS;AACjC,UAAQ,MAAR;GACI,KAAK,WAAW,QACZ,QAAO,SAAS,QAAQ;GAC5B,KAAK,WAAW,WACZ,QAAO,YAAY;GACvB,KAAK,WAAW,cACZ,QAAO,OAAO,YAAY,YAAY,SAAS,QAAQ;GAC3D,KAAK,WAAW;GAChB,KAAK,WAAW,aACZ,QAAQ,MAAM,QAAQ,QAAQ,KACzB,OAAO,QAAQ,OAAO,YAClB,OAAO,QAAQ,OAAO,YACnBD,kBAAgB,QAAQ,QAAQ,GAAG,KAAK;GACxD,KAAK,WAAW;GAChB,KAAK,WAAW,WACZ,QAAO,MAAM,QAAQ,QAAQ;;;;;;CAMzC,UAAU;AACN,MAAI,KAAK,eAAe;AACpB,QAAK,cAAc,wBAAwB;AAC3C,QAAK,gBAAgB;;;;;;;;;;;;AAYjC,IAAM,sBAAN,MAA0B;CACtB,YAAY,QAAQ;AAChB,OAAK,SAAS;AACd,OAAK,UAAU,EAAE;AACjB,OAAK,YAAY;;;;;;;;;;CAUrB,eAAe,SAAS;AACpB,OAAK,QAAQ,KAAK,QAAQ;AAC1B,MAAI,KAAK,QAAQ,WAAW,KAAK,UAAU,aAAa;GAEpD,MAAM,SAAS,kBAAkB,KAAK,WAAW,KAAK,QAAQ;AAC9D,QAAK,wBAAwB;AAC7B,UAAO;;AAEX,SAAO;;;;;CAKX,yBAAyB;AACrB,OAAK,YAAY;AACjB,OAAK,UAAU,EAAE;;;;;;ACpTzB,SAAgB,GAAG,KAAK,IAAI,IAAI;AAC5B,KAAI,GAAG,IAAI,GAAG;AACd,QAAO,SAAS,aAAa;AACzB,MAAI,IAAI,IAAI,GAAG;;;;;;;;;;ACIvB,IAAM,kBAAkB,OAAO,OAAO;CAClC,SAAS;CACT,eAAe;CACf,YAAY;CACZ,eAAe;CAEf,aAAa;CACb,gBAAgB;CACnB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;AAyBF,IAAa,SAAb,cAA4B,QAAQ;;;;CAIhC,YAAY,IAAI,KAAK,MAAM;AACvB,SAAO;;;;;;;;;;;;;;;AAeP,OAAK,YAAY;;;;;AAKjB,OAAK,YAAY;;;;AAIjB,OAAK,gBAAgB,EAAE;;;;AAIvB,OAAK,aAAa,EAAE;;;;;;;AAOpB,OAAK,SAAS,EAAE;;;;;AAKhB,OAAK,YAAY;AACjB,OAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAwBX,OAAK,OAAO,EAAE;AACd,OAAK,QAAQ,EAAE;AACf,OAAK,KAAK;AACV,OAAK,MAAM;AACX,MAAI,QAAQ,KAAK,KACb,MAAK,OAAO,KAAK;AAErB,OAAK,QAAQ,OAAO,OAAO,EAAE,EAAE,KAAK;AACpC,MAAI,KAAK,GAAG,aACR,MAAK,MAAM;;;;;;;;;;;;;;;;CAgBnB,IAAI,eAAe;AACf,SAAO,CAAC,KAAK;;;;;;;CAOjB,YAAY;AACR,MAAI,KAAK,KACL;EACJ,MAAM,KAAK,KAAK;AAChB,OAAK,OAAO;GACR,GAAG,IAAI,QAAQ,KAAK,OAAO,KAAK,KAAK,CAAC;GACtC,GAAG,IAAI,UAAU,KAAK,SAAS,KAAK,KAAK,CAAC;GAC1C,GAAG,IAAI,SAAS,KAAK,QAAQ,KAAK,KAAK,CAAC;GACxC,GAAG,IAAI,SAAS,KAAK,QAAQ,KAAK,KAAK,CAAC;GAC3C;;;;;;;;;;;;;;;;;;;CAmBL,IAAI,SAAS;AACT,SAAO,CAAC,CAAC,KAAK;;;;;;;;;;;;CAYlB,UAAU;AACN,MAAI,KAAK,UACL,QAAO;AACX,OAAK,WAAW;AAChB,MAAI,CAAC,KAAK,GAAG,iBACT,MAAK,GAAG,MAAM;AAClB,MAAI,WAAW,KAAK,GAAG,YACnB,MAAK,QAAQ;AACjB,SAAO;;;;;CAKX,OAAO;AACH,SAAO,KAAK,SAAS;;;;;;;;;;;;;;;;;CAiBzB,KAAK,GAAG,MAAM;AACV,OAAK,QAAQ,UAAU;AACvB,OAAK,KAAK,MAAM,MAAM,KAAK;AAC3B,SAAO;;;;;;;;;;;;;;;;;;;CAmBX,KAAK,IAAI,GAAG,MAAM;EACd,IAAI,IAAI,IAAI;AACZ,MAAI,gBAAgB,eAAe,GAAG,CAClC,OAAM,IAAI,MAAM,OAAM,GAAG,UAAU,GAAG,8BAA6B;AAEvE,OAAK,QAAQ,GAAG;AAChB,MAAI,KAAK,MAAM,WAAW,CAAC,KAAK,MAAM,aAAa,CAAC,KAAK,MAAM,UAAU;AACrE,QAAK,YAAY,KAAK;AACtB,UAAO;;EAEX,MAAM,SAAS;GACX,MAAM,WAAW;GACjB,MAAM;GACT;AACD,SAAO,UAAU,EAAE;AACnB,SAAO,QAAQ,WAAW,KAAK,MAAM,aAAa;AAElD,MAAI,eAAe,OAAO,KAAK,KAAK,SAAS,IAAI;GAC7C,MAAM,KAAK,KAAK;GAChB,MAAM,MAAM,KAAK,KAAK;AACtB,QAAK,qBAAqB,IAAI,IAAI;AAClC,UAAO,KAAK;;EAEhB,MAAM,uBAAuB,MAAM,KAAK,KAAK,GAAG,YAAY,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,eAAe,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG;EACnJ,MAAM,cAAc,KAAK,aAAa,GAAG,KAAK,KAAK,GAAG,YAAY,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,iBAAiB;AAEvH,MADsB,KAAK,MAAM,YAAY,CAAC,qBAC3B,YAEV,aAAa;AAClB,QAAK,wBAAwB,OAAO;AACpC,QAAK,OAAO,OAAO;QAGnB,MAAK,WAAW,KAAK,OAAO;AAEhC,OAAK,QAAQ,EAAE;AACf,SAAO;;;;;CAKX,qBAAqB,IAAI,KAAK;EAC1B,IAAI;EACJ,MAAM,WAAW,KAAK,KAAK,MAAM,aAAa,QAAQ,OAAO,KAAK,IAAI,KAAK,KAAK,MAAM;AACtF,MAAI,YAAY,QAAW;AACvB,QAAK,KAAK,MAAM;AAChB;;EAGJ,MAAM,QAAQ,KAAK,GAAG,mBAAmB;AACrC,UAAO,KAAK,KAAK;AACjB,QAAK,IAAI,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,IACxC,KAAI,KAAK,WAAW,GAAG,OAAO,GAC1B,MAAK,WAAW,OAAO,GAAG,EAAE;AAGpC,OAAI,KAAK,sBAAM,IAAI,MAAM,0BAA0B,CAAC;KACrD,QAAQ;EACX,MAAM,MAAM,GAAG,SAAS;AAEpB,QAAK,GAAG,eAAe,MAAM;AAC7B,OAAI,MAAM,MAAM,KAAK;;AAEzB,KAAG,YAAY;AACf,OAAK,KAAK,MAAM;;;;;;;;;;;;;;;;;;CAkBpB,YAAY,IAAI,GAAG,MAAM;AACrB,SAAO,IAAI,SAAS,SAAS,WAAW;GACpC,MAAM,MAAM,MAAM,SAAS;AACvB,WAAO,OAAO,OAAO,KAAK,GAAG,QAAQ,KAAK;;AAE9C,MAAG,YAAY;AACf,QAAK,KAAK,GAAG;AACb,QAAK,KAAK,IAAI,GAAG,KAAK;IACxB;;;;;;;CAON,YAAY,MAAM;EACd,IAAI;AACJ,MAAI,OAAO,KAAK,KAAK,SAAS,OAAO,WACjC,OAAM,KAAK,KAAK;EAEpB,MAAM,SAAS;GACX,IAAI,KAAK;GACT,UAAU;GACV,SAAS;GACT;GACA,OAAO,OAAO,OAAO,EAAE,WAAW,MAAM,EAAE,KAAK,MAAM;GACxD;AACD,OAAK,MAAM,KAAK,GAAG,iBAAiB;AAChC,OAAI,WAAW,KAAK,OAAO,GAEvB;AAGJ,OADiB,QAAQ,MAErB;QAAI,OAAO,WAAW,KAAK,MAAM,SAAS;AACtC,UAAK,OAAO,OAAO;AACnB,SAAI,IACA,KAAI,IAAI;;UAIf;AACD,SAAK,OAAO,OAAO;AACnB,QAAI,IACA,KAAI,MAAM,GAAG,aAAa;;AAGlC,UAAO,UAAU;AACjB,UAAO,KAAK,aAAa;IAC3B;AACF,OAAK,OAAO,KAAK,OAAO;AACxB,OAAK,aAAa;;;;;;;;CAQtB,YAAY,QAAQ,OAAO;AACvB,MAAI,CAAC,KAAK,aAAa,KAAK,OAAO,WAAW,EAC1C;EAEJ,MAAM,SAAS,KAAK,OAAO;AAC3B,MAAI,OAAO,WAAW,CAAC,MACnB;AAEJ,SAAO,UAAU;AACjB,SAAO;AACP,OAAK,QAAQ,OAAO;AACpB,OAAK,KAAK,MAAM,MAAM,OAAO,KAAK;;;;;;;;CAQtC,OAAO,QAAQ;AACX,SAAO,MAAM,KAAK;AAClB,OAAK,GAAG,QAAQ,OAAO;;;;;;;CAO3B,SAAS;AACL,MAAI,OAAO,KAAK,QAAQ,WACpB,MAAK,MAAM,SAAS;AAChB,QAAK,mBAAmB,KAAK;IAC/B;MAGF,MAAK,mBAAmB,KAAK,KAAK;;;;;;;;CAS1C,mBAAmB,MAAM;AACrB,OAAK,OAAO;GACR,MAAM,WAAW;GACjB,MAAM,KAAK,OACL,OAAO,OAAO;IAAE,KAAK,KAAK;IAAM,QAAQ,KAAK;IAAa,EAAE,KAAK,GACjE;GACT,CAAC;;;;;;;;CAQN,QAAQ,KAAK;AACT,MAAI,CAAC,KAAK,UACN,MAAK,aAAa,iBAAiB,IAAI;;;;;;;;;CAU/C,QAAQ,QAAQ,aAAa;AACzB,OAAK,YAAY;AACjB,SAAO,KAAK;AACZ,OAAK,aAAa,cAAc,QAAQ,YAAY;AACpD,OAAK,YAAY;;;;;;;;CAQrB,aAAa;AACT,SAAO,KAAK,KAAK,KAAK,CAAC,SAAS,OAAO;AAEnC,OAAI,CADe,KAAK,WAAW,MAAM,WAAW,OAAO,OAAO,GAAG,KAAK,GAAG,EAC5D;IAEb,MAAM,MAAM,KAAK,KAAK;AACtB,WAAO,KAAK,KAAK;AACjB,QAAI,IAAI,UACJ,KAAI,KAAK,sBAAM,IAAI,MAAM,+BAA+B,CAAC;;IAGnE;;;;;;;;CAQN,SAAS,QAAQ;AAEb,MAAI,EADkB,OAAO,QAAQ,KAAK,KAEtC;AACJ,UAAQ,OAAO,MAAf;GACI,KAAK,WAAW;AACZ,QAAI,OAAO,QAAQ,OAAO,KAAK,IAC3B,MAAK,UAAU,OAAO,KAAK,KAAK,OAAO,KAAK,IAAI;QAGhD,MAAK,aAAa,iCAAiB,IAAI,MAAM,4LAA4L,CAAC;AAE9O;GACJ,KAAK,WAAW;GAChB,KAAK,WAAW;AACZ,SAAK,QAAQ,OAAO;AACpB;GACJ,KAAK,WAAW;GAChB,KAAK,WAAW;AACZ,SAAK,MAAM,OAAO;AAClB;GACJ,KAAK,WAAW;AACZ,SAAK,cAAc;AACnB;GACJ,KAAK,WAAW;AACZ,SAAK,SAAS;IACd,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK,QAAQ;AAE1C,QAAI,OAAO,OAAO,KAAK;AACvB,SAAK,aAAa,iBAAiB,IAAI;AACvC;;;;;;;;;CASZ,QAAQ,QAAQ;EACZ,MAAM,OAAO,OAAO,QAAQ,EAAE;AAC9B,MAAI,QAAQ,OAAO,GACf,MAAK,KAAK,KAAK,IAAI,OAAO,GAAG,CAAC;AAElC,MAAI,KAAK,UACL,MAAK,UAAU,KAAK;MAGpB,MAAK,cAAc,KAAK,OAAO,OAAO,KAAK,CAAC;;CAGpD,UAAU,MAAM;AACZ,MAAI,KAAK,iBAAiB,KAAK,cAAc,QAAQ;GACjD,MAAM,YAAY,KAAK,cAAc,OAAO;AAC5C,QAAK,MAAM,YAAY,UACnB,UAAS,MAAM,MAAM,KAAK;;AAGlC,QAAM,KAAK,MAAM,MAAM,KAAK;AAC5B,MAAI,KAAK,QAAQ,KAAK,UAAU,OAAO,KAAK,KAAK,SAAS,OAAO,SAC7D,MAAK,cAAc,KAAK,KAAK,SAAS;;;;;;;CAQ9C,IAAI,IAAI;EACJ,MAAME,SAAO;EACb,IAAI,OAAO;AACX,SAAO,SAAU,GAAG,MAAM;AAEtB,OAAI,KACA;AACJ,UAAO;AACP,UAAK,OAAO;IACR,MAAM,WAAW;IACb;IACJ,MAAM;IACT,CAAC;;;;;;;;;CASV,MAAM,QAAQ;EACV,MAAM,MAAM,KAAK,KAAK,OAAO;AAC7B,MAAI,OAAO,QAAQ,WACf;AAEJ,SAAO,KAAK,KAAK,OAAO;AAExB,MAAI,IAAI,UACJ,QAAO,KAAK,QAAQ,KAAK;AAG7B,MAAI,MAAM,MAAM,OAAO,KAAK;;;;;;;CAOhC,UAAU,IAAI,KAAK;AACf,OAAK,KAAK;AACV,OAAK,YAAY,OAAO,KAAK,SAAS;AACtC,OAAK,OAAO;AACZ,OAAK,YAAY;AACjB,OAAK,cAAc;AACnB,OAAK,aAAa,UAAU;AAC5B,OAAK,YAAY,KAAK;;;;;;;CAO1B,eAAe;AACX,OAAK,cAAc,SAAS,SAAS,KAAK,UAAU,KAAK,CAAC;AAC1D,OAAK,gBAAgB,EAAE;AACvB,OAAK,WAAW,SAAS,WAAW;AAChC,QAAK,wBAAwB,OAAO;AACpC,QAAK,OAAO,OAAO;IACrB;AACF,OAAK,aAAa,EAAE;;;;;;;CAOxB,eAAe;AACX,OAAK,SAAS;AACd,OAAK,QAAQ,uBAAuB;;;;;;;;;CASxC,UAAU;AACN,MAAI,KAAK,MAAM;AAEX,QAAK,KAAK,SAAS,eAAe,YAAY,CAAC;AAC/C,QAAK,OAAO;;AAEhB,OAAK,GAAG,YAAY,KAAK;;;;;;;;;;;;;;;;;;CAkB7B,aAAa;AACT,MAAI,KAAK,UACL,MAAK,OAAO,EAAE,MAAM,WAAW,YAAY,CAAC;AAGhD,OAAK,SAAS;AACd,MAAI,KAAK,UAEL,MAAK,QAAQ,uBAAuB;AAExC,SAAO;;;;;;;CAOX,QAAQ;AACJ,SAAO,KAAK,YAAY;;;;;;;;;;;CAW5B,SAAS,UAAU;AACf,OAAK,MAAM,WAAW;AACtB,SAAO;;;;;;;;;;;CAWX,IAAI,WAAW;AACX,OAAK,MAAM,WAAW;AACtB,SAAO;;;;;;;;;;;;;;;CAeX,QAAQ,SAAS;AACb,OAAK,MAAM,UAAU;AACrB,SAAO;;;;;;;;;;;;;CAaX,MAAM,UAAU;AACZ,OAAK,gBAAgB,KAAK,iBAAiB,EAAE;AAC7C,OAAK,cAAc,KAAK,SAAS;AACjC,SAAO;;;;;;;;;;;;;CAaX,WAAW,UAAU;AACjB,OAAK,gBAAgB,KAAK,iBAAiB,EAAE;AAC7C,OAAK,cAAc,QAAQ,SAAS;AACpC,SAAO;;;;;;;;;;;;;;;;;;;;CAoBX,OAAO,UAAU;AACb,MAAI,CAAC,KAAK,cACN,QAAO;AAEX,MAAI,UAAU;GACV,MAAM,YAAY,KAAK;AACvB,QAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,IAClC,KAAI,aAAa,UAAU,IAAI;AAC3B,cAAU,OAAO,GAAG,EAAE;AACtB,WAAO;;QAKf,MAAK,gBAAgB,EAAE;AAE3B,SAAO;;;;;;CAMX,eAAe;AACX,SAAO,KAAK,iBAAiB,EAAE;;;;;;;;;;;;;;;CAenC,cAAc,UAAU;AACpB,OAAK,wBAAwB,KAAK,yBAAyB,EAAE;AAC7D,OAAK,sBAAsB,KAAK,SAAS;AACzC,SAAO;;;;;;;;;;;;;;;CAeX,mBAAmB,UAAU;AACzB,OAAK,wBAAwB,KAAK,yBAAyB,EAAE;AAC7D,OAAK,sBAAsB,QAAQ,SAAS;AAC5C,SAAO;;;;;;;;;;;;;;;;;;;;CAoBX,eAAe,UAAU;AACrB,MAAI,CAAC,KAAK,sBACN,QAAO;AAEX,MAAI,UAAU;GACV,MAAM,YAAY,KAAK;AACvB,QAAK,IAAI,IAAI,GAAG,IAAI,UAAU,QAAQ,IAClC,KAAI,aAAa,UAAU,IAAI;AAC3B,cAAU,OAAO,GAAG,EAAE;AACtB,WAAO;;QAKf,MAAK,wBAAwB,EAAE;AAEnC,SAAO;;;;;;CAMX,uBAAuB;AACnB,SAAO,KAAK,yBAAyB,EAAE;;;;;;;;;CAS3C,wBAAwB,QAAQ;AAC5B,MAAI,KAAK,yBAAyB,KAAK,sBAAsB,QAAQ;GACjE,MAAM,YAAY,KAAK,sBAAsB,OAAO;AACpD,QAAK,MAAM,YAAY,UACnB,UAAS,MAAM,MAAM,OAAO,KAAK;;;;;;;;;;;;;;;;;;ACl2BjD,SAAgB,QAAQ,MAAM;AAC1B,QAAO,QAAQ,EAAE;AACjB,MAAK,KAAK,KAAK,OAAO;AACtB,MAAK,MAAM,KAAK,OAAO;AACvB,MAAK,SAAS,KAAK,UAAU;AAC7B,MAAK,SAAS,KAAK,SAAS,KAAK,KAAK,UAAU,IAAI,KAAK,SAAS;AAClE,MAAK,WAAW;;;;;;;;AAQpB,QAAQ,UAAU,WAAW,WAAY;CACrC,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,WAAW;AACzD,KAAI,KAAK,QAAQ;EACb,IAAI,OAAO,KAAK,QAAQ;EACxB,IAAI,YAAY,KAAK,MAAM,OAAO,KAAK,SAAS,GAAG;AACnD,QAAM,KAAK,MAAM,OAAO,GAAG,GAAG,MAAM,IAAI,KAAK,YAAY,KAAK;;AAElE,QAAO,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG;;;;;;;AAOpC,QAAQ,UAAU,QAAQ,WAAY;AAClC,MAAK,WAAW;;;;;;;AAOpB,QAAQ,UAAU,SAAS,SAAU,KAAK;AACtC,MAAK,KAAK;;;;;;;AAOd,QAAQ,UAAU,SAAS,SAAU,KAAK;AACtC,MAAK,MAAM;;;;;;;AAOf,QAAQ,UAAU,YAAY,SAAU,QAAQ;AAC5C,MAAK,SAAS;;;;;AC1DlB,IAAa,UAAb,cAA6B,QAAQ;CACjC,YAAY,KAAK,MAAM;EACnB,IAAI;AACJ,SAAO;AACP,OAAK,OAAO,EAAE;AACd,OAAK,OAAO,EAAE;AACd,MAAI,OAAO,aAAa,OAAO,KAAK;AAChC,UAAO;AACP,SAAM;;AAEV,SAAO,QAAQ,EAAE;AACjB,OAAK,OAAO,KAAK,QAAQ;AACzB,OAAK,OAAO;AACZ,wBAAsB,MAAM,KAAK;AACjC,OAAK,aAAa,KAAK,iBAAiB,MAAM;AAC9C,OAAK,qBAAqB,KAAK,wBAAwB,SAAS;AAChE,OAAK,kBAAkB,KAAK,qBAAqB,IAAK;AACtD,OAAK,qBAAqB,KAAK,wBAAwB,IAAK;AAC5D,OAAK,qBAAqB,KAAK,KAAK,yBAAyB,QAAQ,OAAO,KAAK,IAAI,KAAK,GAAI;AAC9F,OAAK,UAAU,IAAI,QAAQ;GACvB,KAAK,KAAK,mBAAmB;GAC7B,KAAK,KAAK,sBAAsB;GAChC,QAAQ,KAAK,qBAAqB;GACrC,CAAC;AACF,OAAK,QAAQ,QAAQ,KAAK,UAAU,MAAQ,KAAK,QAAQ;AACzD,OAAK,cAAc;AACnB,OAAK,MAAM;EACX,MAAM,UAAU,KAAK,UAAUC;AAC/B,OAAK,UAAU,IAAI,QAAQ,SAAS;AACpC,OAAK,UAAU,IAAI,QAAQ,SAAS;AACpC,OAAK,eAAe,KAAK,gBAAgB;AACzC,MAAI,KAAK,aACL,MAAK,MAAM;;CAEnB,aAAa,GAAG;AACZ,MAAI,CAAC,UAAU,OACX,QAAO,KAAK;AAChB,OAAK,gBAAgB,CAAC,CAAC;AACvB,MAAI,CAAC,EACD,MAAK,gBAAgB;AAEzB,SAAO;;CAEX,qBAAqB,GAAG;AACpB,MAAI,MAAM,OACN,QAAO,KAAK;AAChB,OAAK,wBAAwB;AAC7B,SAAO;;CAEX,kBAAkB,GAAG;EACjB,IAAI;AACJ,MAAI,MAAM,OACN,QAAO,KAAK;AAChB,OAAK,qBAAqB;AAC1B,GAAC,KAAK,KAAK,aAAa,QAAQ,OAAO,KAAK,KAAa,GAAG,OAAO,EAAE;AACrE,SAAO;;CAEX,oBAAoB,GAAG;EACnB,IAAI;AACJ,MAAI,MAAM,OACN,QAAO,KAAK;AAChB,OAAK,uBAAuB;AAC5B,GAAC,KAAK,KAAK,aAAa,QAAQ,OAAO,KAAK,KAAa,GAAG,UAAU,EAAE;AACxE,SAAO;;CAEX,qBAAqB,GAAG;EACpB,IAAI;AACJ,MAAI,MAAM,OACN,QAAO,KAAK;AAChB,OAAK,wBAAwB;AAC7B,GAAC,KAAK,KAAK,aAAa,QAAQ,OAAO,KAAK,KAAa,GAAG,OAAO,EAAE;AACrE,SAAO;;CAEX,QAAQ,GAAG;AACP,MAAI,CAAC,UAAU,OACX,QAAO,KAAK;AAChB,OAAK,WAAW;AAChB,SAAO;;;;;;;;CAQX,uBAAuB;AAEnB,MAAI,CAAC,KAAK,iBACN,KAAK,iBACL,KAAK,QAAQ,aAAa,EAE1B,MAAK,WAAW;;;;;;;;;CAUxB,KAAK,IAAI;AACL,MAAI,CAAC,KAAK,YAAY,QAAQ,OAAO,CACjC,QAAO;AACX,OAAK,SAAS,IAAIC,SAAO,KAAK,KAAK,KAAK,KAAK;EAC7C,MAAM,SAAS,KAAK;EACpB,MAAMC,SAAO;AACb,OAAK,cAAc;AACnB,OAAK,gBAAgB;EAErB,MAAM,iBAAiB,GAAG,QAAQ,QAAQ,WAAY;AAClD,UAAK,QAAQ;AACb,SAAM,IAAI;IACZ;EACF,MAAM,WAAW,QAAQ;AACrB,QAAK,SAAS;AACd,QAAK,cAAc;AACnB,QAAK,aAAa,SAAS,IAAI;AAC/B,OAAI,GACA,IAAG,IAAI;OAIP,MAAK,sBAAsB;;EAInC,MAAM,WAAW,GAAG,QAAQ,SAAS,QAAQ;AAC7C,MAAI,UAAU,KAAK,UAAU;GACzB,MAAM,UAAU,KAAK;GAErB,MAAM,QAAQ,KAAK,mBAAmB;AAClC,oBAAgB;AAChB,4BAAQ,IAAI,MAAM,UAAU,CAAC;AAC7B,WAAO,OAAO;MACf,QAAQ;AACX,OAAI,KAAK,KAAK,UACV,OAAM,OAAO;AAEjB,QAAK,KAAK,WAAW;AACjB,SAAK,eAAe,MAAM;KAC5B;;AAEN,OAAK,KAAK,KAAK,eAAe;AAC9B,OAAK,KAAK,KAAK,SAAS;AACxB,SAAO;;;;;;;;CAQX,QAAQ,IAAI;AACR,SAAO,KAAK,KAAK,GAAG;;;;;;;CAOxB,SAAS;AAEL,OAAK,SAAS;AAEd,OAAK,cAAc;AACnB,OAAK,aAAa,OAAO;EAEzB,MAAM,SAAS,KAAK;AACpB,OAAK,KAAK,KAAK,GAAG,QAAQ,QAAQ,KAAK,OAAO,KAAK,KAAK,CAAC,EAAE,GAAG,QAAQ,QAAQ,KAAK,OAAO,KAAK,KAAK,CAAC,EAAE,GAAG,QAAQ,SAAS,KAAK,QAAQ,KAAK,KAAK,CAAC,EAAE,GAAG,QAAQ,SAAS,KAAK,QAAQ,KAAK,KAAK,CAAC,EAEjM,GAAG,KAAK,SAAS,WAAW,KAAK,UAAU,KAAK,KAAK,CAAC,CAAC;;;;;;;CAO3D,SAAS;AACL,OAAK,aAAa,OAAO;;;;;;;CAO7B,OAAO,MAAM;AACT,MAAI;AACA,QAAK,QAAQ,IAAI,KAAK;WAEnB,GAAG;AACN,QAAK,QAAQ,eAAe,EAAE;;;;;;;;CAQtC,UAAU,QAAQ;AAEd,iBAAe;AACX,QAAK,aAAa,UAAU,OAAO;KACpC,KAAK,aAAa;;;;;;;CAOzB,QAAQ,KAAK;AACT,OAAK,aAAa,SAAS,IAAI;;;;;;;;CAQnC,OAAO,KAAK,MAAM;EACd,IAAI,SAAS,KAAK,KAAK;AACvB,MAAI,CAAC,QAAQ;AACT,YAAS,IAAI,OAAO,MAAM,KAAK,KAAK;AACpC,QAAK,KAAK,OAAO;aAEZ,KAAK,gBAAgB,CAAC,OAAO,OAClC,QAAO,SAAS;AAEpB,SAAO;;;;;;;;CAQX,SAAS,QAAQ;EACb,MAAM,OAAO,OAAO,KAAK,KAAK,KAAK;AACnC,OAAK,MAAM,OAAO,KAEd,KADe,KAAK,KAAK,KACd,OACP;AAGR,OAAK,QAAQ;;;;;;;;CAQjB,QAAQ,QAAQ;EACZ,MAAM,iBAAiB,KAAK,QAAQ,OAAO,OAAO;AAClD,OAAK,IAAI,IAAI,GAAG,IAAI,eAAe,QAAQ,IACvC,MAAK,OAAO,MAAM,eAAe,IAAI,OAAO,QAAQ;;;;;;;CAQ5D,UAAU;AACN,OAAK,KAAK,SAAS,eAAe,YAAY,CAAC;AAC/C,OAAK,KAAK,SAAS;AACnB,OAAK,QAAQ,SAAS;;;;;;;CAO1B,SAAS;AACL,OAAK,gBAAgB;AACrB,OAAK,gBAAgB;AACrB,OAAK,QAAQ,eAAe;;;;;;;CAOhC,aAAa;AACT,SAAO,KAAK,QAAQ;;;;;;;;;;;CAWxB,QAAQ,QAAQ,aAAa;EACzB,IAAI;AACJ,OAAK,SAAS;AACd,GAAC,KAAK,KAAK,YAAY,QAAQ,OAAO,KAAK,KAAa,GAAG,OAAO;AAClE,OAAK,QAAQ,OAAO;AACpB,OAAK,cAAc;AACnB,OAAK,aAAa,SAAS,QAAQ,YAAY;AAC/C,MAAI,KAAK,iBAAiB,CAAC,KAAK,cAC5B,MAAK,WAAW;;;;;;;CAQxB,YAAY;AACR,MAAI,KAAK,iBAAiB,KAAK,cAC3B,QAAO;EACX,MAAMA,SAAO;AACb,MAAI,KAAK,QAAQ,YAAY,KAAK,uBAAuB;AACrD,QAAK,QAAQ,OAAO;AACpB,QAAK,aAAa,mBAAmB;AACrC,QAAK,gBAAgB;SAEpB;GACD,MAAM,QAAQ,KAAK,QAAQ,UAAU;AACrC,QAAK,gBAAgB;GACrB,MAAM,QAAQ,KAAK,mBAAmB;AAClC,QAAIA,OAAK,cACL;AACJ,SAAK,aAAa,qBAAqBA,OAAK,QAAQ,SAAS;AAE7D,QAAIA,OAAK,cACL;AACJ,WAAK,MAAM,QAAQ;AACf,SAAI,KAAK;AACL,aAAK,gBAAgB;AACrB,aAAK,WAAW;AAChB,WAAK,aAAa,mBAAmB,IAAI;WAGzC,QAAK,aAAa;MAExB;MACH,MAAM;AACT,OAAI,KAAK,KAAK,UACV,OAAM,OAAO;AAEjB,QAAK,KAAK,WAAW;AACjB,SAAK,eAAe,MAAM;KAC5B;;;;;;;;CAQV,cAAc;EACV,MAAM,UAAU,KAAK,QAAQ;AAC7B,OAAK,gBAAgB;AACrB,OAAK,QAAQ,OAAO;AACpB,OAAK,aAAa,aAAa,QAAQ;;;;;;;;;ACtW/C,IAAM,QAAQ,EAAE;AAChB,SAAS,OAAO,KAAK,MAAM;AACvB,KAAI,OAAO,QAAQ,UAAU;AACzB,SAAO;AACP,QAAM;;AAEV,QAAO,QAAQ,EAAE;CACjB,MAAM,SAAS,IAAI,KAAK,KAAK,QAAQ,aAAa;CAClD,MAAM,SAAS,OAAO;CACtB,MAAM,KAAK,OAAO;CAClB,MAAM,OAAO,OAAO;CACpB,MAAM,gBAAgB,MAAM,OAAO,QAAQ,MAAM,IAAI;CACrD,MAAM,gBAAgB,KAAK,YACvB,KAAK,2BACL,UAAU,KAAK,aACf;CACJ,IAAI;AACJ,KAAI,cACA,MAAK,IAAI,QAAQ,QAAQ,KAAK;MAE7B;AACD,MAAI,CAAC,MAAM,IACP,OAAM,MAAM,IAAI,QAAQ,QAAQ,KAAK;AAEzC,OAAK,MAAM;;AAEf,KAAI,OAAO,SAAS,CAAC,KAAK,MACtB,MAAK,QAAQ,OAAO;AAExB,QAAO,GAAG,OAAO,OAAO,MAAM,KAAK;;AAIvC,OAAO,OAAO,QAAQ;CAClB;CACA;CACA,IAAI;CACJ,SAAS;CACZ,CAAC"}