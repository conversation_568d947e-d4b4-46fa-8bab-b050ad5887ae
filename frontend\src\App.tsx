import React, { useState, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';
import './App.css';
import EmailList from './components/EmailList';
import SearchBar from './components/SearchBar';
import FilterPanel from './components/FilterPanel';
import EmailDetail from './components/EmailDetail';
import Dashboard from './components/Dashboard';
import { Email, SearchQuery } from './types';
import { emailService } from './services/emailService';

function App() {
  const [emails, setEmails] = useState<Email[]>([]);
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null);
  const [searchQuery, setSearchQuery] = useState<SearchQuery>({});
  const [loading, setLoading] = useState(false);
  const [socket, setSocket] = useState<Socket | null>(null);
  const [currentView, setCurrentView] = useState<'dashboard' | 'emails'>('dashboard');
  const [totalEmails, setTotalEmails] = useState(0);

  useEffect(() => {
    // Initialize socket connection
    const socketConnection = io('http://localhost:3000');
    setSocket(socketConnection);

    // Listen for new emails
    socketConnection.on('new_email', (data) => {
      console.log('New email received:', data);
      setEmails(prev => [data.email, ...prev]);
    });

    // Listen for connection status
    socketConnection.on('connected', (data) => {
      console.log('Connected to server:', data);
    });

    // Load initial emails
    loadEmails();

    return () => {
      socketConnection.disconnect();
    };
  }, []);

  const loadEmails = async (query: SearchQuery = {}) => {
    setLoading(true);
    try {
      const result = await emailService.searchEmails({
        ...query,
        limit: 20,
        offset: 0,
      });
      setEmails(result.emails);
      setTotalEmails(result.total);
    } catch (error) {
      console.error('Error loading emails:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query: SearchQuery) => {
    setSearchQuery(query);
    loadEmails(query);
  };

  const handleEmailSelect = (email: Email) => {
    setSelectedEmail(email);
    // Mark as read if not already
    if (!email.isRead) {
      emailService.updateEmail(email.id, { isRead: true });
      setEmails(prev => 
        prev.map(e => e.id === email.id ? { ...e, isRead: true } : e)
      );
    }
  };

  const handleEmailUpdate = (emailId: string, updates: Partial<Email>) => {
    setEmails(prev => 
      prev.map(e => e.id === emailId ? { ...e, ...updates } : e)
    );
    if (selectedEmail && selectedEmail.id === emailId) {
      setSelectedEmail(prev => prev ? { ...prev, ...updates } : null);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">📧 Onebox</h1>
              <span className="ml-2 text-sm text-gray-500">Email System</span>
            </div>
            <nav className="flex space-x-4">
              <button
                onClick={() => setCurrentView('dashboard')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentView === 'dashboard'
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Dashboard
              </button>
              <button
                onClick={() => setCurrentView('emails')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentView === 'emails'
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Emails
              </button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentView === 'dashboard' ? (
          <Dashboard />
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <FilterPanel onFilterChange={handleSearch} />
            </div>

            {/* Email List */}
            <div className="lg:col-span-1">
              <div className="card">
                <div className="card-header">
                  <SearchBar onSearch={handleSearch} />
                </div>
                <div className="card-body p-0">
                  <EmailList
                    emails={emails}
                    loading={loading}
                    onEmailSelect={handleEmailSelect}
                    selectedEmailId={selectedEmail?.id}
                  />
                </div>
              </div>
            </div>

            {/* Email Detail */}
            <div className="lg:col-span-2">
              {selectedEmail ? (
                <EmailDetail
                  email={selectedEmail}
                  onUpdate={handleEmailUpdate}
                />
              ) : (
                <div className="card">
                  <div className="card-body text-center py-12">
                    <div className="text-gray-400 text-6xl mb-4">📧</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Select an email
                    </h3>
                    <p className="text-gray-500">
                      Choose an email from the list to view its details
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
