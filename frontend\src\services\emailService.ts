import axios from 'axios';
import { Email, SearchQuery, SearchResult, APIResponse, AIAnalysis, SuggestedReply, EmailStats } from '../types';

const API_BASE_URL = 'http://localhost:3000/api';

class EmailService {
  private api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Get emails with filtering and pagination
  async getEmails(params: SearchQuery = {}): Promise<SearchResult> {
    const response = await this.api.get<APIResponse<SearchResult>>('/emails', { params });
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to fetch emails');
    }
    return response.data.data!;
  }

  // Search emails
  async searchEmails(query: SearchQuery): Promise<SearchResult> {
    const response = await this.api.post<APIResponse<SearchResult>>('/search', query);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Search failed');
    }
    return response.data.data!;
  }

  // Get specific email
  async getEmail(id: string): Promise<Email> {
    const response = await this.api.get<APIResponse<Email>>(`/emails/${id}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to fetch email');
    }
    return response.data.data!;
  }

  // Update email
  async updateEmail(id: string, updates: Partial<Email>): Promise<void> {
    const response = await this.api.put<APIResponse>(`/emails/${id}`, updates);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to update email');
    }
  }

  // Delete email
  async deleteEmail(id: string): Promise<void> {
    const response = await this.api.delete<APIResponse>(`/emails/${id}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to delete email');
    }
  }

  // Get emails by category
  async getEmailsByCategory(category: string, limit: number = 10): Promise<Email[]> {
    const response = await this.api.get<APIResponse<Email[]>>(`/emails/category/${category}`, {
      params: { limit },
    });
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to fetch emails by category');
    }
    return response.data.data!;
  }

  // Get email statistics
  async getEmailStats(): Promise<EmailStats> {
    const response = await this.api.get<APIResponse<any>>('/emails/stats');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to fetch email statistics');
    }
    
    // Transform Elasticsearch aggregations to our format
    const stats = response.data.data;
    return {
      totalEmails: stats.total_emails?.value || 0,
      unreadCount: stats.unread_count?.doc_count || 0,
      starredCount: stats.starred_count?.doc_count || 0,
      byCategory: {
        interested: stats.by_category?.buckets?.find((b: any) => b.key === 'interested')?.doc_count || 0,
        meeting_booked: stats.by_category?.buckets?.find((b: any) => b.key === 'meeting_booked')?.doc_count || 0,
        not_interested: stats.by_category?.buckets?.find((b: any) => b.key === 'not_interested')?.doc_count || 0,
        spam: stats.by_category?.buckets?.find((b: any) => b.key === 'spam')?.doc_count || 0,
        out_of_office: stats.by_category?.buckets?.find((b: any) => b.key === 'out_of_office')?.doc_count || 0,
      },
      byAccount: stats.by_account?.buckets?.reduce((acc: any, bucket: any) => {
        acc[bucket.key] = bucket.doc_count;
        return acc;
      }, {}) || {},
      recentActivity: stats.recent_emails?.buckets?.map((bucket: any) => ({
        date: bucket.key_as_string,
        count: bucket.doc_count,
      })) || [],
    };
  }

  // Categorize email manually
  async categorizeEmail(id: string): Promise<AIAnalysis> {
    const response = await this.api.post<APIResponse<AIAnalysis>>(`/emails/${id}/categorize`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to categorize email');
    }
    return response.data.data!;
  }

  // Get suggested reply
  async getSuggestedReply(id: string, knowledgeBase?: string[]): Promise<SuggestedReply> {
    const response = await this.api.post<APIResponse<SuggestedReply>>(`/emails/${id}/suggest-reply`, {
      knowledgeBase,
    });
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to generate reply suggestion');
    }
    return response.data.data!;
  }

  // Analyze email
  async analyzeEmail(id: string): Promise<{
    sentiment: { sentiment: string; score: number; reasoning: string };
    keyInfo: { entities: string[]; keyPhrases: string[]; actionItems: string[]; urgency: string };
  }> {
    const response = await this.api.post<APIResponse<any>>(`/emails/${id}/analyze`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to analyze email');
    }
    return response.data.data!;
  }

  // Bulk categorize emails
  async bulkCategorizeEmails(emailIds: string[]): Promise<{ [emailId: string]: AIAnalysis }> {
    const response = await this.api.post<APIResponse<{ [emailId: string]: AIAnalysis }>>('/emails/bulk-categorize', {
      emailIds,
    });
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to bulk categorize emails');
    }
    return response.data.data!;
  }

  // Get search suggestions
  async getSearchSuggestions(query: string): Promise<string[]> {
    const response = await this.api.get<APIResponse<string[]>>('/search/suggestions', {
      params: { query },
    });
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to get search suggestions');
    }
    return response.data.data!;
  }

  // Get filter options
  async getFilterOptions(): Promise<{
    accounts: { id: string; count: number }[];
    categories: { id: string; name: string; count: number }[];
    folders: { id: string; count: number }[];
  }> {
    const response = await this.api.get<APIResponse<any>>('/search/filters');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to get filter options');
    }
    return response.data.data!;
  }

  // Mark email as read/unread
  async markAsRead(id: string, isRead: boolean = true): Promise<void> {
    await this.updateEmail(id, { isRead });
  }

  // Star/unstar email
  async toggleStar(id: string, isStarred: boolean): Promise<void> {
    await this.updateEmail(id, { isStarred });
  }

  // Format date for display
  formatDate(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInHours = (now.getTime() - d.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return d.toLocaleDateString();
    }
  }

  // Get category display name
  getCategoryDisplayName(category: string): string {
    const names: { [key: string]: string } = {
      interested: 'Interested',
      meeting_booked: 'Meeting Booked',
      not_interested: 'Not Interested',
      spam: 'Spam',
      out_of_office: 'Out of Office',
    };
    return names[category] || category;
  }

  // Get category color class
  getCategoryColorClass(category: string): string {
    const colors: { [key: string]: string } = {
      interested: 'badge-interested',
      meeting_booked: 'badge-meeting-booked',
      not_interested: 'badge-not-interested',
      spam: 'badge-spam',
      out_of_office: 'badge-out-of-office',
    };
    return colors[category] || 'badge-out-of-office';
  }
}

export const emailService = new EmailService();
