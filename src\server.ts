import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';

import { config, validateConfig } from '@/config';
import logger from '@/utils/logger';
import { EmailSyncService } from '@/services/EmailSyncService';
import { ElasticsearchService } from '@/services/ElasticsearchService';
import { AIService } from '@/services/AIService';
import { NotificationService } from '@/services/NotificationService';

// Import routes
import emailRoutes from '@/routes/emails';
import searchRoutes from '@/routes/search';

class OneboxServer {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private emailSyncService: EmailSyncService;
  private elasticsearchService: ElasticsearchService;
  private aiService: AIService;
  private notificationService: NotificationService;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });
    
    this.setupMiddleware();
    this.setupRoutes();
    this.initializeServices();
  }

  private setupMiddleware(): void {
    this.app.use(helmet());
    this.app.use(cors());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));
    
    // Request logging
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: config.nodeEnv,
      });
    });

    // API routes
    this.app.use('/api/emails', emailRoutes);
    this.app.use('/api/search', searchRoutes);

    // Serve frontend static files in production
    if (config.nodeEnv === 'production') {
      this.app.use(express.static('frontend/dist'));
      this.app.get('*', (req, res) => {
        res.sendFile('frontend/dist/index.html');
      });
    }

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: 'Route not found',
      });
    });

    // Error handler
    this.app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      logger.error('Unhandled error:', err);
      res.status(500).json({
        success: false,
        error: config.nodeEnv === 'production' ? 'Internal server error' : err.message,
      });
    });
  }

  private async initializeServices(): Promise<void> {
    try {
      logger.info('Initializing services...');

      // Initialize Elasticsearch
      this.elasticsearchService = new ElasticsearchService();
      await this.elasticsearchService.initialize();

      // Initialize AI Service
      this.aiService = new AIService();
      await this.aiService.initialize();

      // Initialize Notification Service
      this.notificationService = new NotificationService();

      // Initialize Email Sync Service
      this.emailSyncService = new EmailSyncService(
        this.elasticsearchService,
        this.aiService,
        this.notificationService,
        this.io
      );

      logger.info('All services initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize services:', error);
      process.exit(1);
    }
  }

  private setupSocketIO(): void {
    this.io.on('connection', (socket) => {
      logger.info(`Client connected: ${socket.id}`);

      socket.on('disconnect', () => {
        logger.info(`Client disconnected: ${socket.id}`);
      });

      // Send initial data to client
      socket.emit('connected', {
        message: 'Connected to Onebox Email System',
        timestamp: new Date().toISOString(),
      });
    });
  }

  public async start(): Promise<void> {
    try {
      // Validate configuration
      validateConfig();

      // Setup Socket.IO
      this.setupSocketIO();

      // Start the server
      this.server.listen(config.port, () => {
        logger.info(`🚀 Onebox Email System started on port ${config.port}`);
        logger.info(`📧 Environment: ${config.nodeEnv}`);
        logger.info(`🔍 Elasticsearch: ${config.elasticsearch.url}`);
      });

      // Start email synchronization
      await this.emailSyncService.startSynchronization();

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  public async stop(): Promise<void> {
    logger.info('Shutting down Onebox Email System...');
    
    if (this.emailSyncService) {
      await this.emailSyncService.stopSynchronization();
    }
    
    this.server.close(() => {
      logger.info('Server closed');
      process.exit(0);
    });
  }
}

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received');
  if (server) {
    await server.stop();
  }
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received');
  if (server) {
    await server.stop();
  }
});

// Start the server
const server = new OneboxServer();
server.start().catch((error) => {
  logger.error('Failed to start server:', error);
  process.exit(1);
});

export default OneboxServer;
