// Email related types
export interface EmailAccount {
  id: string;
  email: string;
  provider: 'gmail' | 'outlook';
  isActive: boolean;
  lastSyncDate?: Date;
}

export interface Email {
  id: string;
  messageId: string;
  accountId: string;
  subject: string;
  from: EmailAddress;
  to: <PERSON>ail<PERSON>ddress[];
  cc?: EmailAddress[];
  bcc?: EmailAddress[];
  date: Date;
  body: {
    text?: string;
    html?: string;
  };
  attachments?: Attachment[];
  folder: string;
  flags: string[];
  uid: number;
  aiCategory?: AICategory;
  isRead: boolean;
  isStarred: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface EmailAddress {
  name?: string;
  address: string;
}

export interface Attachment {
  filename: string;
  contentType: string;
  size: number;
  contentId?: string;
}

// AI related types
export type AICategory = 'interested' | 'meeting_booked' | 'not_interested' | 'spam' | 'out_of_office';

export interface AIAnalysis {
  category: AICategory;
  confidence: number;
  reasoning: string;
  suggestedReply?: string;
  extractedInfo?: {
    meetingTime?: string;
    contactInfo?: string;
    urgency?: 'low' | 'medium' | 'high';
  };
}

// Search related types
export interface SearchQuery {
  query?: string;
  accountId?: string;
  folder?: string;
  category?: AICategory;
  dateFrom?: Date;
  dateTo?: Date;
  isRead?: boolean;
  isStarred?: boolean;
  limit?: number;
  offset?: number;
}

export interface SearchResult {
  emails: Email[];
  total: number;
  took: number;
}

// Notification types
export interface SlackNotification {
  channel: string;
  text: string;
  email: Email;
  category: AICategory;
}

export interface WebhookPayload {
  event: 'email_categorized' | 'new_email' | 'email_replied';
  email: Email;
  category?: AICategory;
  timestamp: Date;
}

// RAG related types
export interface KnowledgeBase {
  id: string;
  content: string;
  metadata: {
    type: 'product_info' | 'company_info' | 'template' | 'faq';
    tags: string[];
    lastUpdated: Date;
  };
  embedding?: number[];
}

export interface SuggestedReply {
  content: string;
  confidence: number;
  sources: string[];
  reasoning: string;
}

// API Response types
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Component Props types
export interface EmailListProps {
  emails: Email[];
  loading: boolean;
  onEmailSelect: (email: Email) => void;
  selectedEmailId?: string;
}

export interface EmailDetailProps {
  email: Email;
  onUpdate: (emailId: string, updates: Partial<Email>) => void;
}

export interface SearchBarProps {
  onSearch: (query: SearchQuery) => void;
}

export interface FilterPanelProps {
  onFilterChange: (query: SearchQuery) => void;
}

export interface DashboardProps {}

// Statistics types
export interface EmailStats {
  totalEmails: number;
  unreadCount: number;
  starredCount: number;
  byCategory: {
    [key in AICategory]: number;
  };
  byAccount: {
    [accountId: string]: number;
  };
  recentActivity: {
    date: string;
    count: number;
  }[];
}
