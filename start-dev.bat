@echo off
echo 🚀 Starting Onebox Email System (Development Mode)
echo.

echo 📝 Checking configuration...
if not exist .env (
    echo ❌ .env file not found. Creating from template...
    copy .env.example .env
    echo ⚠️  Please edit .env file with your OpenAI API key and email credentials
    echo.
    pause
    exit /b 1
)

echo 📦 Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)

echo 📦 Installing frontend dependencies...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

echo 🔨 Building backend...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Failed to build backend
    pause
    exit /b 1
)

echo.
echo ✅ Setup complete! Starting services...
echo.
echo 📱 Frontend will be available at: http://localhost:5173
echo 🔧 Backend API will be available at: http://localhost:3000
echo.

echo Starting backend...
start "Onebox Backend" cmd /k "npm run dev"

echo Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo Starting frontend...
start "Onebox Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo 🎉 Onebox Email System is starting!
echo.
echo ℹ️  Two command windows will open:
echo    1. Backend (Node.js server)
echo    2. Frontend (React development server)
echo.
echo 🔧 Using mock services (no Elasticsearch/Redis required)
echo 📧 Sample emails will be available for testing
echo.
echo Press any key to exit this window...
pause > nul
