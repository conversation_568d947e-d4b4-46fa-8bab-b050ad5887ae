# Installation
> `npm install --save @types/mailparser`

# Summary
This package contains type definitions for mailparser (https://github.com/nodemailer/mailparser).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mailparser.

### Additional Details
 * Last updated: Tue, 06 May 2025 08:38:38 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node), [iconv-lite](https://npmjs.com/package/iconv-lite)

# Credits
These definitions were written by [<PERSON>](https://github.com/psnider), [<PERSON><PERSON>](https://github.com/Avol-V), and [<PERSON><PERSON>](https://github.com/peter<PERSON><PERSON><PERSON><PERSON>).
