{"version": 3, "file": "react_jsx-dev-runtime.js", "names": [], "sources": ["../../react/cjs/react-jsx-dev-runtime.development.js", "../../react/jsx-dev-runtime.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "mappings": ";;;;;AAWA,EACG,WAAY;EACX,SAAS,yBAAyB,MAAM;AACtC,OAAI,QAAQ,KAAM,QAAO;AACzB,OAAI,eAAe,OAAO,KACxB,QAAO,KAAK,aAAa,yBACrB,OACA,KAAK,eAAe,KAAK,QAAQ;AACvC,OAAI,aAAa,OAAO,KAAM,QAAO;AACrC,WAAQ,MAAR;IACE,KAAK,oBACH,QAAO;IACT,KAAK,oBACH,QAAO;IACT,KAAK,uBACH,QAAO;IACT,KAAK,oBACH,QAAO;IACT,KAAK,yBACH,QAAO;IACT,KAAK,oBACH,QAAO;;AAEX,OAAI,aAAa,OAAO,KACtB,SACG,aAAa,OAAO,KAAK,OACxB,QAAQ,MACN,oHACD,EACH,KAAK,UALP;IAOE,KAAK,kBACH,QAAO;IACT,KAAK,mBACH,SAAQ,KAAK,eAAe,aAAa;IAC3C,KAAK,oBACH,SAAQ,KAAK,SAAS,eAAe,aAAa;IACpD,KAAK;KACH,IAAI,YAAY,KAAK;AACrB,YAAO,KAAK;AACZ,cACI,OAAO,UAAU,eAAe,UAAU,QAAQ,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM;AACrD,YAAO;IACT,KAAK,gBACH,QACG,YAAY,KAAK,eAAe,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,KAAK,IAAI;IAE/C,KAAK;AACH,iBAAY,KAAK;AACjB,YAAO,KAAK;AACZ,SAAI;AACF,aAAO,yBAAyB,KAAK,UAAU,CAAC;cACzC,GAAG;;AAElB,UAAO;;EAET,SAAS,mBAAmB,OAAO;AACjC,UAAO,KAAK;;EAEd,SAAS,uBAAuB,OAAO;AACrC,OAAI;AACF,uBAAmB,MAAM;IACzB,IAAI,2BAA2B,CAAC;YACzB,GAAG;AACV,+BAA2B,CAAC;;AAE9B,OAAI,0BAA0B;AAC5B,+BAA2B;IAC3B,IAAI,wBAAwB,yBAAyB;IACrD,IAAI,oCACD,eAAe,OAAO,UACrB,OAAO,eACP,MAAM,OAAO,gBACf,MAAM,YAAY,QAClB;AACF,0BAAsB,KACpB,0BACA,4GACA,kCACD;AACD,WAAO,mBAAmB,MAAM;;;EAGpC,SAAS,YAAY,MAAM;AACzB,OAAI,SAAS,oBAAqB,QAAO;AACzC,OACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,aAAa,gBAElB,QAAO;AACT,OAAI;IACF,IAAI,OAAO,yBAAyB,KAAK;AACzC,WAAO,OAAO,MAAM,OAAO,MAAM;YAC1B,GAAG;AACV,WAAO;;;EAGX,SAAS,WAAW;GAClB,IAAI,aAAa,qBAAqB;AACtC,UAAO,SAAS,aAAa,OAAO,WAAW,UAAU;;EAE3D,SAAS,eAAe;AACtB,UAAO,MAAM,wBAAwB;;EAEvC,SAAS,YAAY,QAAQ;AAC3B,OAAI,eAAe,KAAK,QAAQ,MAAM,EAAE;IACtC,IAAI,SAAS,OAAO,yBAAyB,QAAQ,MAAM,CAAC;AAC5D,QAAI,UAAU,OAAO,eAAgB,QAAO,CAAC;;AAE/C,UAAO,KAAK,MAAM,OAAO;;EAE3B,SAAS,2BAA2B,OAAO,aAAa;GACtD,SAAS,wBAAwB;AAC/B,mCACI,6BAA6B,CAAC,GAChC,QAAQ,MACN,2OACA,YACD;;AAEL,yBAAsB,iBAAiB,CAAC;AACxC,UAAO,eAAe,OAAO,OAAO;IAClC,KAAK;IACL,cAAc,CAAC;IAChB,CAAC;;EAEJ,SAAS,yCAAyC;GAChD,IAAI,gBAAgB,yBAAyB,KAAK,KAAK;AACvD,0BAAuB,mBACnB,uBAAuB,iBAAiB,CAAC,GAC3C,QAAQ,MACN,8IACD;AACH,mBAAgB,KAAK,MAAM;AAC3B,UAAO,KAAK,MAAM,gBAAgB,gBAAgB;;EAEpD,SAAS,aACP,MACA,KACA,MACA,QACA,OACA,OACA,YACA,WACA;AACA,UAAO,MAAM;AACb,UAAO;IACL,UAAU;IACJ;IACD;IACE;IACP,QAAQ;IACT;AACD,aAAU,KAAK,MAAM,OAAO,OAAO,QAC/B,OAAO,eAAe,MAAM,OAAO;IACjC,YAAY,CAAC;IACb,KAAK;IACN,CAAC,GACF,OAAO,eAAe,MAAM,OAAO;IAAE,YAAY,CAAC;IAAG,OAAO;IAAM,CAAC;AACvE,QAAK,SAAS,EAAE;AAChB,UAAO,eAAe,KAAK,QAAQ,aAAa;IAC9C,cAAc,CAAC;IACf,YAAY,CAAC;IACb,UAAU,CAAC;IACX,OAAO;IACR,CAAC;AACF,UAAO,eAAe,MAAM,cAAc;IACxC,cAAc,CAAC;IACf,YAAY,CAAC;IACb,UAAU,CAAC;IACX,OAAO;IACR,CAAC;AACF,UAAO,eAAe,MAAM,eAAe;IACzC,cAAc,CAAC;IACf,YAAY,CAAC;IACb,UAAU,CAAC;IACX,OAAO;IACR,CAAC;AACF,UAAO,eAAe,MAAM,cAAc;IACxC,cAAc,CAAC;IACf,YAAY,CAAC;IACb,UAAU,CAAC;IACX,OAAO;IACR,CAAC;AACF,UAAO,WAAW,OAAO,OAAO,KAAK,MAAM,EAAE,OAAO,OAAO,KAAK;AAChE,UAAO;;EAET,SAAS,WACP,MACA,QACA,UACA,kBACA,QACA,MACA,YACA,WACA;GACA,IAAI,WAAW,OAAO;AACtB,OAAI,KAAK,MAAM,SACb,KAAI,iBACF,KAAI,YAAY,SAAS,EAAE;AACzB,SACE,mBAAmB,GACnB,mBAAmB,SAAS,QAC5B,mBAEA,mBAAkB,SAAS,kBAAkB;AAC/C,WAAO,UAAU,OAAO,OAAO,SAAS;SAExC,SAAQ,MACN,uJACD;OACA,mBAAkB,SAAS;AAClC,OAAI,eAAe,KAAK,QAAQ,MAAM,EAAE;AACtC,eAAW,yBAAyB,KAAK;IACzC,IAAI,OAAO,OAAO,KAAK,OAAO,CAAC,OAAO,SAAU,GAAG;AACjD,YAAO,UAAU;MACjB;AACF,uBACE,IAAI,KAAK,SACL,oBAAoB,KAAK,KAAK,UAAU,GAAG,WAC3C;AACN,0BAAsB,WAAW,sBAC7B,OACA,IAAI,KAAK,SAAS,MAAM,KAAK,KAAK,UAAU,GAAG,WAAW,MAC5D,QAAQ,MACN,qOACA,kBACA,UACA,MACA,SACD,EACA,sBAAsB,WAAW,oBAAoB,CAAC;;AAE3D,cAAW;AACX,QAAK,MAAM,aACR,uBAAuB,SAAS,EAAG,WAAW,KAAK;AACtD,eAAY,OAAO,KAChB,uBAAuB,OAAO,IAAI,EAAG,WAAW,KAAK,OAAO;AAC/D,OAAI,SAAS,QAAQ;AACnB,eAAW,EAAE;AACb,SAAK,IAAI,YAAY,OACnB,WAAU,aAAa,SAAS,YAAY,OAAO;SAChD,YAAW;AAClB,eACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,eAAe,KAAK,QAAQ,YACjC,KACL;AACH,UAAO,aACL,MACA,UACA,MACA,QACA,UAAU,EACV,UACA,YACA,UACD;;EAEH,SAAS,kBAAkB,MAAM;AAC/B,gBAAa,OAAO,QAClB,SAAS,QACT,KAAK,aAAa,sBAClB,KAAK,WACJ,KAAK,OAAO,YAAY;;EAE7B,IAAI,yBACF,qBAAqB,OAAO,IAAI,6BAA6B,EAC7D,oBAAoB,OAAO,IAAI,eAAe,EAC9C,sBAAsB,OAAO,IAAI,iBAAiB,EAClD,yBAAyB,OAAO,IAAI,oBAAoB,EACxD,sBAAsB,OAAO,IAAI,iBAAiB;EAEpD,IAAI,sBAAsB,OAAO,IAAI,iBAAiB,EACpD,qBAAqB,OAAO,IAAI,gBAAgB,EAChD,yBAAyB,OAAO,IAAI,oBAAoB,EACxD,sBAAsB,OAAO,IAAI,iBAAiB,EAClD,2BAA2B,OAAO,IAAI,sBAAsB,EAC5D,kBAAkB,OAAO,IAAI,aAAa,EAC1C,kBAAkB,OAAO,IAAI,aAAa,EAC1C,sBAAsB,OAAO,IAAI,iBAAiB,EAClD,yBAAyB,OAAO,IAAI,yBAAyB,EAC7D,uBACE,MAAM,iEACR,iBAAiB,OAAO,UAAU,gBAClC,cAAc,MAAM,SACpB,aAAa,QAAQ,aACjB,QAAQ,aACR,WAAY;AACV,UAAO;;AAEf,UAAQ,EACN,0BAA0B,SAAU,mBAAmB;AACrD,UAAO,mBAAmB;KAE7B;EACD,IAAI;EACJ,IAAI,yBAAyB,EAAE;EAC/B,IAAI,yBAAyB,MAAM,yBAAyB,KAC1D,OACA,aACD,EAAE;EACH,IAAI,wBAAwB,WAAW,YAAY,aAAa,CAAC;EACjE,IAAI,wBAAwB,EAAE;AAC9B,UAAQ,WAAW;AACnB,UAAQ,SAAS,SACf,MACA,QACA,UACA,kBACA,QACA,MACA;GACA,IAAI,mBACF,MAAM,qBAAqB;AAC7B,UAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,wBAAwB,GAC9B,wBACJ,mBAAmB,WAAW,YAAY,KAAK,CAAC,GAAG,sBACpD;;KAED;;;;;;ACvVJ,QAAO"}