import { Email, SearchQuery, SearchResult } from '@/types';
import logger from '@/utils/logger';

/**
 * Mock Elasticsearch Service for development without Elasticsearch
 * Stores emails in memory - not for production use
 */
export class MockElasticsearchService {
  private emails: Email[] = [];
  private initialized = false;

  public async initialize(): Promise<void> {
    if (this.initialized) return;

    logger.info('🔄 Initializing Mock Elasticsearch Service (Development Mode)');
    
    // Create some sample emails for testing
    this.createSampleEmails();
    
    this.initialized = true;
    logger.info('✅ Mock Elasticsearch Service initialized with sample data');
  }

  public async indexEmail(email: Email): Promise<void> {
    try {
      // Remove existing email with same ID
      this.emails = this.emails.filter(e => e.id !== email.id);
      
      // Add new email
      this.emails.unshift(email);
      
      // Keep only last 1000 emails to prevent memory issues
      if (this.emails.length > 1000) {
        this.emails = this.emails.slice(0, 1000);
      }
      
      logger.debug(`Indexed email: ${email.subject}`);
    } catch (error) {
      logger.error('Error indexing email:', error);
      throw error;
    }
  }

  public async searchEmails(query: SearchQuery): Promise<SearchResult> {
    try {
      let filteredEmails = [...this.emails];

      // Apply filters
      if (query.query) {
        const searchTerm = query.query.toLowerCase();
        filteredEmails = filteredEmails.filter(email => 
          email.subject.toLowerCase().includes(searchTerm) ||
          email.body.text?.toLowerCase().includes(searchTerm) ||
          email.from.address.toLowerCase().includes(searchTerm) ||
          email.from.name?.toLowerCase().includes(searchTerm)
        );
      }

      if (query.account) {
        filteredEmails = filteredEmails.filter(email => email.account === query.account);
      }

      if (query.category) {
        filteredEmails = filteredEmails.filter(email => email.aiCategory === query.category);
      }

      if (query.isRead !== undefined) {
        filteredEmails = filteredEmails.filter(email => email.isRead === query.isRead);
      }

      if (query.isStarred !== undefined) {
        filteredEmails = filteredEmails.filter(email => email.isStarred === query.isStarred);
      }

      if (query.dateFrom) {
        filteredEmails = filteredEmails.filter(email => email.date >= query.dateFrom!);
      }

      if (query.dateTo) {
        filteredEmails = filteredEmails.filter(email => email.date <= query.dateTo!);
      }

      // Sort by date (newest first)
      filteredEmails.sort((a, b) => b.date.getTime() - a.date.getTime());

      // Apply pagination
      const offset = query.offset || 0;
      const limit = query.limit || 20;
      const paginatedEmails = filteredEmails.slice(offset, offset + limit);

      return {
        emails: paginatedEmails,
        total: filteredEmails.length,
        offset,
        limit,
      };
    } catch (error) {
      logger.error('Error searching emails:', error);
      throw error;
    }
  }

  public async getEmailById(id: string): Promise<Email | null> {
    return this.emails.find(email => email.id === id) || null;
  }

  public async updateEmail(id: string, updates: Partial<Email>): Promise<void> {
    const emailIndex = this.emails.findIndex(email => email.id === id);
    if (emailIndex !== -1) {
      this.emails[emailIndex] = { ...this.emails[emailIndex], ...updates };
      logger.debug(`Updated email: ${id}`);
    }
  }

  public async deleteEmail(id: string): Promise<void> {
    this.emails = this.emails.filter(email => email.id !== id);
    logger.debug(`Deleted email: ${id}`);
  }

  public async getStats(): Promise<any> {
    const stats = {
      total: this.emails.length,
      unread: this.emails.filter(e => !e.isRead).length,
      starred: this.emails.filter(e => e.isStarred).length,
      categories: {
        interested: this.emails.filter(e => e.aiCategory === 'interested').length,
        meeting_booked: this.emails.filter(e => e.aiCategory === 'meeting_booked').length,
        not_interested: this.emails.filter(e => e.aiCategory === 'not_interested').length,
        spam: this.emails.filter(e => e.aiCategory === 'spam').length,
        out_of_office: this.emails.filter(e => e.aiCategory === 'out_of_office').length,
        uncategorized: this.emails.filter(e => !e.aiCategory).length,
      },
      accounts: this.getAccountStats(),
    };

    return stats;
  }

  private getAccountStats(): Record<string, number> {
    const accountStats: Record<string, number> = {};
    this.emails.forEach(email => {
      accountStats[email.account] = (accountStats[email.account] || 0) + 1;
    });
    return accountStats;
  }

  private createSampleEmails(): void {
    const sampleEmails: Partial<Email>[] = [
      {
        id: 'sample-1',
        subject: 'Interested in your email management solution',
        from: { address: '<EMAIL>', name: 'John Doe' },
        to: [{ address: '<EMAIL>', name: 'Your Company' }],
        body: { text: 'Hi, I saw your email management solution and I\'m very interested. Can we schedule a demo?' },
        date: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        account: '<EMAIL>',
        isRead: false,
        isStarred: false,
        aiCategory: 'interested',
      },
      {
        id: 'sample-2',
        subject: 'Meeting scheduled for tomorrow',
        from: { address: '<EMAIL>', name: 'Sarah Smith' },
        to: [{ address: '<EMAIL>', name: 'Your Company' }],
        body: { text: 'Thanks for the demo! I\'ve scheduled our meeting for tomorrow at 2 PM. Looking forward to it.' },
        date: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
        account: '<EMAIL>',
        isRead: true,
        isStarred: true,
        aiCategory: 'meeting_booked',
      },
      {
        id: 'sample-3',
        subject: 'Not interested at this time',
        from: { address: '<EMAIL>', name: 'Mike Johnson' },
        to: [{ address: '<EMAIL>', name: 'Your Company' }],
        body: { text: 'Thank you for reaching out, but we\'re not interested in this solution at this time.' },
        date: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
        account: '<EMAIL>',
        isRead: true,
        isStarred: false,
        aiCategory: 'not_interested',
      },
      {
        id: 'sample-4',
        subject: 'Out of Office: Will return Monday',
        from: { address: '<EMAIL>', name: 'Lisa Brown' },
        to: [{ address: '<EMAIL>', name: 'Your Company' }],
        body: { text: 'I am currently out of office and will return on Monday. I will respond to your email then.' },
        date: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 hours ago
        account: '<EMAIL>',
        isRead: false,
        isStarred: false,
        aiCategory: 'out_of_office',
      },
      {
        id: 'sample-5',
        subject: 'URGENT: You have won $1,000,000!!!',
        from: { address: '<EMAIL>', name: 'Lottery Winner' },
        to: [{ address: '<EMAIL>', name: 'Your Company' }],
        body: { text: 'Congratulations! You have won our lottery! Click here to claim your prize...' },
        date: new Date(Date.now() - 1000 * 60 * 60 * 12), // 12 hours ago
        account: '<EMAIL>',
        isRead: false,
        isStarred: false,
        aiCategory: 'spam',
      },
    ];

    this.emails = sampleEmails.map(email => ({
      ...email,
      attachments: [],
    })) as Email[];
  }

  public getEmailCount(): number {
    return this.emails.length;
  }
}
