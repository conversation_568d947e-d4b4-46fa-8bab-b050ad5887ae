// Email related types
export interface EmailAccount {
  id: string;
  email: string;
  password: string;
  provider: 'gmail' | 'outlook';
  imapConfig: IMAPConfig;
  isActive: boolean;
  lastSyncDate?: Date;
}

export interface IMAPConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

export interface Email {
  id: string;
  messageId: string;
  accountId: string;
  subject: string;
  from: EmailAddress;
  to: EmailAddress[];
  cc?: EmailAddress[];
  bcc?: EmailAddress[];
  date: Date;
  body: {
    text?: string | undefined;
    html?: string | undefined;
  };
  attachments?: Attachment[];
  folder: string;
  flags: string[];
  uid: number;
  aiCategory?: AICategory;
  isRead: boolean;
  isStarred: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface EmailAddress {
  name?: string;
  address: string;
}

export interface Attachment {
  filename: string;
  contentType: string;
  size: number;
  contentId?: string;
}

// AI related types
export type AICategory = 'interested' | 'meeting_booked' | 'not_interested' | 'spam' | 'out_of_office';

export interface AIAnalysis {
  category: AICategory;
  confidence: number;
  reasoning: string;
  suggestedReply?: string;
  extractedInfo?: {
    meetingTime?: string;
    contactInfo?: string;
    urgency?: 'low' | 'medium' | 'high';
  };
}

// Search related types
export interface SearchQuery {
  query?: string;
  accountId?: string;
  folder?: string;
  category?: AICategory;
  dateFrom?: Date;
  dateTo?: Date;
  isRead?: boolean;
  isStarred?: boolean;
  limit?: number;
  offset?: number;
}

export interface SearchResult {
  emails: Email[];
  total: number;
  took: number;
}

// Notification types
export interface SlackNotification {
  channel: string;
  text: string;
  email: Email;
  category: AICategory;
}

export interface WebhookPayload {
  event: 'email_categorized' | 'new_email' | 'email_replied';
  email: Email;
  category?: AICategory;
  timestamp: Date;
}

// RAG related types
export interface KnowledgeBase {
  id: string;
  content: string;
  metadata: {
    type: 'product_info' | 'company_info' | 'template' | 'faq';
    tags: string[];
    lastUpdated: Date;
  };
  embedding?: number[];
}

export interface SuggestedReply {
  content: string;
  confidence: number;
  sources: string[];
  reasoning: string;
}

// API Response types
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Configuration types
export interface AppConfig {
  port: number;
  nodeEnv: string;
  useMockServices?: boolean;
  elasticsearch: {
    url: string;
    username?: string | undefined;
    password?: string | undefined;
  };
  redis: {
    url: string;
  };
  openai: {
    apiKey: string;
    model: string;
  };
  slack: {
    webhookUrl: string;
    channel: string;
  };
  webhook: {
    externalUrl: string;
  };
  jwt: {
    secret: string;
    expiresIn: string;
  };
}
